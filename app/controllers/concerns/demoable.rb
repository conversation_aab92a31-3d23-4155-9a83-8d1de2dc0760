# rubocop:disable Metrics/ModuleLength
module Demoable
  extend ActiveSupport::Concern

  def self.randomized_robinhood_data
    timeframe = 1.month
    end_date = Date.today
    start_date = end_date - timeframe
    dates = (start_date..end_date).to_a
    start_balance = 4200

    formatted_data_points = dates.map { |date| { value: start_balance, time: date.to_s } }

    # apply more diverse fluctuations with random significant events
    formatted_data_points.each_cons(2) do |data_point, next_data_point|
      fluctuation = rand(0.85..1.15)

      # Introduce different types of events
      event_probability = rand
      significant_event = case event_probability
                          when 0..0.05 then rand(0.50..1.50) # 5% chance of a major event
                          when 0.05..0.15 then rand(0.70..1.30) # 10% chance of a moderate event
                          when 0.15..0.30 then rand(0.90..1.10) # 15% chance of a minor event
                          else 1 # 70% chance of no significant event
                          end

      next_data_point[:value] = (data_point[:value] * fluctuation * significant_event).round(2)
    end
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def self.past_calendar_events
    [
      {
        summary: 'Sync with Design Team',
        start: '10:00',
        start_full: (DateTime.now - 3.days).in_time_zone('America/New_York').change(hour: 10, min: 0),
        date_time: (DateTime.now - 3.days).in_time_zone('America/New_York').change(hour: 10, min: 0),
        end: '12:00',
        end_full: (DateTime.now - 3.days).change(hour: 12).in_time_zone('America/New_York'),
        description: 'Discuss pixels and such',
        status: 'confirmed'
      },
      {
        summary: 'Play hooky',
        start: '09:00',
        start_full: (DateTime.now - 2.days).in_time_zone('America/New_York').change(hour: 9, min: 0),
        date_time: (DateTime.now - 2.days).in_time_zone('America/New_York').change(hour: 9, min: 0),
        end: '15:00',
        end_full: (DateTime.now - 2.days).change(hour: 15).in_time_zone('America/New_York'),
        description: 'Discuss pixels and such',
        status: 'confirmed'
      },
      {
        summary: "Sarah's birthday party",
        start: '17:00',
        start_full: (DateTime.now - 1.days).in_time_zone('America/New_York').change(hour: 17, min: 0),
        date_time: (DateTime.now - 1.days).in_time_zone('America/New_York').change(hour: 17, min: 0),
        end: '20:00',
        end_full: (DateTime.now - 1.days).change(hour: 20).in_time_zone('America/New_York'),
        description: 'Eat cake IDK',
        status: 'confirmed'
      }
    ]
  end

  def self.calendar_events
    current_month_start = Date.today.beginning_of_month
    current_month_end = Date.today.end_of_month

    [
      # Week 1 - Early month events
      {
        summary: 'New Year Planning Session',
        start: '09:00',
        start_full: (current_month_start + 2.days).in_time_zone('America/New_York').change(hour: 9, min: 0),
        date_time: (current_month_start + 2.days).in_time_zone('America/New_York').change(hour: 9, min: 0),
        end: '11:30',
        end_full: (current_month_start + 2.days).in_time_zone('America/New_York').change(hour: 11, min: 30),
        description: 'Strategic planning session for the upcoming quarter goals and objectives',
        status: 'confirmed'
      },
      {
        summary: 'Client Demo - ACME Corp',
        start: '14:00',
        start_full: (current_month_start + 3.days).in_time_zone('America/New_York').change(hour: 14, min: 0),
        date_time: (current_month_start + 3.days).in_time_zone('America/New_York').change(hour: 14, min: 0),
        end: '15:30',
        end_full: (current_month_start + 3.days).in_time_zone('America/New_York').change(hour: 15, min: 30),
        description: 'Product demonstration for potential enterprise client',
        status: 'confirmed'
      },
      {
        summary: 'Weekend Team Retreat',
        start: '10:00',
        start_full: (current_month_start + 5.days).in_time_zone('America/New_York').change(hour: 10, min: 0),
        date_time: (current_month_start + 5.days).in_time_zone('America/New_York').change(hour: 10, min: 0),
        end: '18:00',
        end_full: (current_month_start + 7.days).in_time_zone('America/New_York').change(hour: 18, min: 0),
        all_day: true,
        description: 'Two-day team building retreat in the mountains',
        status: 'confirmed'
      },

      # Week 2 - Mid-month events
      {
        summary: 'Architecture Review',
        start: '10:30',
        start_full: (current_month_start + 8.days).in_time_zone('America/New_York').change(hour: 10, min: 30),
        date_time: (current_month_start + 8.days).in_time_zone('America/New_York').change(hour: 10, min: 30),
        end: '12:00',
        end_full: (current_month_start + 8.days).in_time_zone('America/New_York').change(hour: 12, min: 0),
        description: 'Technical architecture review for the new microservices implementation',
        status: 'confirmed'
      },
      {
        summary: 'Marketing Campaign Launch',
        start: '13:00',
        start_full: (current_month_start + 9.days).in_time_zone('America/New_York').change(hour: 13, min: 0),
        date_time: (current_month_start + 9.days).in_time_zone('America/New_York').change(hour: 13, min: 0),
        end: '14:30',
        end_full: (current_month_start + 9.days).in_time_zone('America/New_York').change(hour: 14, min: 30),
        description: 'Coordinate the launch of our Q1 marketing campaign across all channels',
        status: 'confirmed'
      },
      {
        summary: 'Investor Relations Call',
        start: '16:00',
        start_full: (current_month_start + 10.days).in_time_zone('America/New_York').change(hour: 16, min: 0),
        date_time: (current_month_start + 10.days).in_time_zone('America/New_York').change(hour: 16, min: 0),
        end: '17:00',
        end_full: (current_month_start + 10.days).in_time_zone('America/New_York').change(hour: 17, min: 0),
        description: 'Monthly update call with key investors and board members',
        status: 'confirmed'
      },
      {
        summary: 'Security Audit',
        start: '09:00',
        start_full: (current_month_start + 11.days).in_time_zone('America/New_York').change(hour: 9, min: 0),
        date_time: (current_month_start + 11.days).in_time_zone('America/New_York').change(hour: 9, min: 0),
        end: '17:00',
        end_full: (current_month_start + 11.days).in_time_zone('America/New_York').change(hour: 17, min: 0),
        all_day: true,
        description: 'Comprehensive security audit of all systems and infrastructure',
        status: 'confirmed'
      },
      {
        summary: 'Product Roadmap Review',
        start: '11:00',
        start_full: (current_month_start + 12.days).in_time_zone('America/New_York').change(hour: 11, min: 0),
        date_time: (current_month_start + 12.days).in_time_zone('America/New_York').change(hour: 11, min: 0),
        end: '13:00',
        end_full: (current_month_start + 12.days).in_time_zone('America/New_York').change(hour: 13, min: 0),
        description: 'Quarterly review of product roadmap and feature prioritization',
        status: 'confirmed'
      },

      # Week 3 - Mid-month events (filling the gap)
      {
        summary: 'Performance Review Session',
        start: '10:00',
        start_full: (current_month_start + 13.days).in_time_zone('America/New_York').change(hour: 10, min: 0),
        date_time: (current_month_start + 13.days).in_time_zone('America/New_York').change(hour: 10, min: 0),
        end: '12:00',
        end_full: (current_month_start + 13.days).in_time_zone('America/New_York').change(hour: 12, min: 0),
        description: 'Annual performance review and goal setting for team members',
        status: 'confirmed'
      },
      {
        summary: 'Budget Planning Workshop',
        start: '14:00',
        start_full: (current_month_start + 14.days).in_time_zone('America/New_York').change(hour: 14, min: 0),
        date_time: (current_month_start + 14.days).in_time_zone('America/New_York').change(hour: 14, min: 0),
        end: '16:30',
        end_full: (current_month_start + 14.days).in_time_zone('America/New_York').change(hour: 16, min: 30),
        description: 'Workshop to plan budget allocation for next quarter projects',
        status: 'confirmed'
      },
      {
        summary: 'Tech Talk: AI in Development',
        start: '11:30',
        start_full: (current_month_start + 15.days).in_time_zone('America/New_York').change(hour: 11, min: 30),
        date_time: (current_month_start + 15.days).in_time_zone('America/New_York').change(hour: 11, min: 30),
        end: '12:30',
        end_full: (current_month_start + 15.days).in_time_zone('America/New_York').change(hour: 12, min: 30),
        description: 'Internal tech talk on implementing AI tools in our development workflow',
        status: 'confirmed'
      },
      {
        summary: 'Client Check-in - Beta Testing',
        start: '15:00',
        start_full: (current_month_start + 16.days).in_time_zone('America/New_York').change(hour: 15, min: 0),
        date_time: (current_month_start + 16.days).in_time_zone('America/New_York').change(hour: 15, min: 0),
        end: '16:00',
        end_full: (current_month_start + 16.days).in_time_zone('America/New_York').change(hour: 16, min: 0),
        description: 'Weekly check-in with beta testing clients to gather feedback',
        status: 'confirmed'
      },
      {
        summary: 'Database Migration Planning',
        start: '09:30',
        start_full: (current_month_start + 17.days).in_time_zone('America/New_York').change(hour: 9, min: 30),
        date_time: (current_month_start + 17.days).in_time_zone('America/New_York').change(hour: 9, min: 30),
        end: '11:00',
        end_full: (current_month_start + 17.days).in_time_zone('America/New_York').change(hour: 11, min: 0),
        description: 'Plan the upcoming database migration and downtime strategy',
        status: 'confirmed'
      },
      {
        summary: 'Team Lunch & Learn',
        start: '12:00',
        start_full: (current_month_start + 18.days).in_time_zone('America/New_York').change(hour: 12, min: 0),
        date_time: (current_month_start + 18.days).in_time_zone('America/New_York').change(hour: 12, min: 0),
        end: '13:30',
        end_full: (current_month_start + 18.days).in_time_zone('America/New_York').change(hour: 13, min: 30),
        description: 'Casual learning session over lunch - discussing industry trends',
        status: 'confirmed'
      },
      {
        summary: 'API Documentation Review',
        start: '16:30',
        start_full: (current_month_start + 19.days).in_time_zone('America/New_York').change(hour: 16, min: 30),
        date_time: (current_month_start + 19.days).in_time_zone('America/New_York').change(hour: 16, min: 30),
        end: '17:30',
        end_full: (current_month_start + 19.days).in_time_zone('America/New_York').change(hour: 17, min: 30),
        description: 'Review and update API documentation for external developers',
        status: 'confirmed'
      },

      # Current week events (redistributed and balanced)
      {
        summary: 'Monthly Catchup with Dev Team',
        start: '10:00',
        start_full: DateTime.now.in_time_zone('America/New_York').change(hour: 10, min: 0),
        date_time: DateTime.now.in_time_zone('America/New_York').change(hour: 10, min: 0),
        end: '11:00',
        end_full: DateTime.now.change(hour: 11).in_time_zone('America/New_York'),
        description: 'Discuss progress and obstacles with the development team',
        status: 'confirmed'
      },
      {
        summary: 'Bath',
        start: '19:45',
        start_full: DateTime.now.in_time_zone('America/New_York').change(hour: 19, min: 45),
        date_time: DateTime.now.in_time_zone('America/New_York').change(hour: 19, min: 45),
        end: '20:15',
        end_full: DateTime.now.in_time_zone('America/New_York').change(hour: 20, min: 15),
        description: "Clean!",
        status: 'confirmed'
      },
      {
        summary: 'Michael OOO',
        start: '10:00',
        start_full: (DateTime.now + 1.day).in_time_zone('America/New_York').change(hour: 10, min: 0),
        date_time: (DateTime.now + 1.day).in_time_zone('America/New_York').change(hour: 10, min: 0),
        end: '11:00',
        end_full: (DateTime.now + 4.days).in_time_zone('America/New_York').change(hour: 11, min: 0),
        all_day: true,
        description: 'Headed to the Bahamas',
        status: 'confirmed'
      },
      {
        summary: 'Coffee with Investors',
        start: '12:30',
        start_full: (DateTime.now + 1.day).in_time_zone('America/New_York').change(hour: 12, min: 30),
        date_time: (DateTime.now + 1.day).in_time_zone('America/New_York').change(hour: 12, min: 30),
        end: '13:30',
        end_full: (DateTime.now + 1.day).in_time_zone('America/New_York').change(hour: 13, min: 30),
        description: 'Quarterly meeting to review financials and forecasts with key investors',
        status: 'confirmed'
      },
      {
        summary: 'Quarterly Strategy Meeting',
        start: '08:15',
        start_full: (DateTime.now + 2.days).in_time_zone('America/New_York').change(hour: 8, min: 15),
        date_time: (DateTime.now + 2.days).in_time_zone('America/New_York').change(hour: 8, min: 15),
        end: '11:00',
        end_full: (DateTime.now + 2.days).in_time_zone('America/New_York').change(hour: 11, min: 0),
        description: 'Extended meeting to review and plan company strategy for the next quarter.',
        status: 'confirmed'
      },
      {
        summary: 'UX Research Presentation',
        start: '15:30',
        start_full: (DateTime.now + 2.days).in_time_zone('America/New_York').change(hour: 15, min: 30),
        date_time: (DateTime.now + 2.days).in_time_zone('America/New_York').change(hour: 15, min: 30),
        end: '17:00',
        end_full: (DateTime.now + 2.days).in_time_zone('America/New_York').change(hour: 17, min: 0),
        description: 'Presentation of user research findings and design recommendations',
        status: 'confirmed'
      },
      {
        summary: 'Project Kickoff',
        start: '14:30',
        start_full: (DateTime.now + 3.days).in_time_zone('America/New_York').change(hour: 14, min: 30),
        date_time: (DateTime.now + 3.days).in_time_zone('America/New_York').change(hour: 14, min: 30),
        end: '16:00',
        end_full: (DateTime.now + 3.days).in_time_zone('America/New_York').change(hour: 16, min: 0),
        description: 'Kickoff meeting for the new client project.',
        status: 'confirmed'
      },
      {
        summary: 'Gratitude Standup',
        start: '15:00',
        start_full: (DateTime.now + 4.days).in_time_zone('America/New_York').change(hour: 15, min: 0),
        date_time: (DateTime.now + 4.days).in_time_zone('America/New_York').change(hour: 15, min: 0),
        end: '16:00',
        end_full: (DateTime.now + 4.days).in_time_zone('America/New_York').change(hour: 16, min: 0),
        description: "Hello everyone! It's officially a good day.",
        status: 'confirmed'
      },
      {
        summary: 'Code Review',
        start: '11:00',
        start_full: (DateTime.now + 5.days).in_time_zone('America/New_York').change(hour: 11, min: 0),
        date_time: (DateTime.now + 5.days).in_time_zone('America/New_York').change(hour: 11, min: 0),
        end: '12:00',
        end_full: (DateTime.now + 5.days).in_time_zone('America/New_York').change(hour: 12, min: 0),
        description: 'Review and discuss recent code changes with the development team.',
        status: 'confirmed'
      },
      {
        summary: 'Hackathon',
        start: '13:00',
        start_full: (DateTime.now + 6.days).in_time_zone('America/New_York').change(hour: 13, min: 0),
        date_time: (DateTime.now + 6.days).in_time_zone('America/New_York').change(hour: 13, min: 0),
        end: '17:00',
        end_full: (DateTime.now + 6.days).in_time_zone('America/New_York').change(hour: 17, min: 0),
        description: 'Team hackathon to explore innovative ideas and build prototypes.',
        status: 'confirmed'
      },
      {
        summary: 'Read a book',
        start: '23:30',
        start_full: (DateTime.now + 6.days).in_time_zone('America/New_York').change(hour: 23, min: 30),
        date_time: (DateTime.now + 6.days).in_time_zone('America/New_York').change(hour: 23, min: 30),
        end: '23:59',
        end_full: (DateTime.now + 6.days).in_time_zone('America/New_York').change(hour: 23, min: 59),
        description: "Get smarter",
        status: 'confirmed'
      },

      # Week 4 & beyond - Future events
      {
        summary: 'All-Hands Meeting',
        start: '10:00',
        start_full: (current_month_start + 20.days).in_time_zone('America/New_York').change(hour: 10, min: 0),
        date_time: (current_month_start + 20.days).in_time_zone('America/New_York').change(hour: 10, min: 0),
        end: '14:00',
        end_full: (current_month_start + 20.days).in_time_zone('America/New_York').change(hour: 14, min: 0),
        description: 'Company-wide meeting to discuss updates, goals, and celebrate achievements.',
        status: 'confirmed'
      },
      {
        summary: 'Training Session',
        start: '15:00',
        start_full: (current_month_start + 20.days).in_time_zone('America/New_York').change(hour: 15, min: 0),
        date_time: (current_month_start + 20.days).in_time_zone('America/New_York').change(hour: 15, min: 0),
        end: '16:30',
        end_full: (current_month_start + 20.days).in_time_zone('America/New_York').change(hour: 16, min: 30),
        description: 'Training on new development tools and methodologies.',
        status: 'confirmed'
      },
      {
        summary: 'User Feedback Session',
        start: '13:30',
        start_full: (current_month_start + 22.days).in_time_zone('America/New_York').change(hour: 13, min: 30),
        date_time: (current_month_start + 22.days).in_time_zone('America/New_York').change(hour: 13, min: 30),
        end: '15:00',
        end_full: (current_month_start + 22.days).in_time_zone('America/New_York').change(hour: 15, min: 0),
        description: 'Review customer feedback and plan product improvements',
        status: 'confirmed'
      },
      {
        summary: 'Sprint Planning',
        start: '09:30',
        start_full: (current_month_start + 23.days).in_time_zone('America/New_York').change(hour: 9, min: 30),
        date_time: (current_month_start + 23.days).in_time_zone('America/New_York').change(hour: 9, min: 30),
        end: '11:00',
        end_full: (current_month_start + 23.days).in_time_zone('America/New_York').change(hour: 11, min: 0),
        description: 'Plan tasks and goals for the upcoming sprint.',
        status: 'confirmed'
      },
      {
        summary: 'One-on-One with John',
        start: '14:30',
        start_full: (current_month_start + 23.days).change(hour: 14, min: 30).in_time_zone('America/New_York'),
        date_time: (current_month_start + 23.days).change(hour: 14, min: 30).in_time_zone('America/New_York'),
        end: '15:00',
        end_full: (current_month_start + 23.days).change(hour: 15, min: 0).in_time_zone('America/New_York'),
        description: 'Weekly check-in with team members.',
        status: 'confirmed'
      },
      {
        summary: 'Weekend Hackathon Finals',
        start: '10:00',
        start_full: (current_month_start + 26.days).in_time_zone('America/New_York').change(hour: 10, min: 0),
        date_time: (current_month_start + 26.days).in_time_zone('America/New_York').change(hour: 10, min: 0),
        end: '18:00',
        end_full: (current_month_start + 26.days).in_time_zone('America/New_York').change(hour: 18, min: 0),
        description: 'Final presentations and judging for the company hackathon',
        status: 'confirmed'
      },
      {
        summary: 'Monthly Review & Planning',
        start: '16:00',
        start_full: (current_month_start + 27.days).in_time_zone('America/New_York').change(hour: 16, min: 0),
        date_time: (current_month_start + 27.days).in_time_zone('America/New_York').change(hour: 16, min: 0),
        end: '17:00',
        end_full: (current_month_start + 27.days).in_time_zone('America/New_York').change(hour: 17, min: 0),
        description: 'Review progress and plan for the upcoming month.',
        status: 'confirmed'
      },
      {
        summary: 'Team Building & Happy Hour',
        start: '17:30',
        start_full: (current_month_start + 27.days).in_time_zone('America/New_York').change(hour: 17, min: 30),
        date_time: (current_month_start + 27.days).in_time_zone('America/New_York').change(hour: 17, min: 30),
        end: '19:00',
        end_full: (current_month_start + 27.days).in_time_zone('America/New_York').change(hour: 19, min: 0),
        description: 'End of month celebration and team bonding',
        status: 'confirmed'
      },

      # End of month events
      {
        summary: 'Month-End Reporting',
        start: '09:00',
        start_full: (current_month_end - 1.day).in_time_zone('America/New_York').change(hour: 9, min: 0),
        date_time: (current_month_end - 1.day).in_time_zone('America/New_York').change(hour: 9, min: 0),
        end: '12:00',
        end_full: (current_month_end - 1.day).in_time_zone('America/New_York').change(hour: 12, min: 0),
        description: 'Compile and review monthly metrics and KPIs',
        status: 'confirmed'
      },
      {
        summary: 'Q1 Preparation Meeting',
        start: '14:00',
        start_full: current_month_end.in_time_zone('America/New_York').change(hour: 14, min: 0),
        date_time: current_month_end.in_time_zone('America/New_York').change(hour: 14, min: 0),
        end: '16:00',
        end_full: current_month_end.in_time_zone('America/New_York').change(hour: 16, min: 0),
        description: 'Final preparations for next quarter initiatives',
        status: 'confirmed'
      }
    ]
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize

  def get_demo_data(keyname, variant_type = nil)
    plugin_config = LOCALS[keyname.to_sym]
    return {} unless plugin_config

    return plugin_config unless plugin_config.key?(:variants)
    return plugin_config[:variants].values.first || {} unless variant_type

    plugin_config[:variants][variant_type.to_sym] || {}
  end

  # rubocop:disable all
  LOCALS = {
    bible_verses: {
      instance_name: 'Word of God',
      verse: {
        reference: "Genesis 37:14",
        text: "And if thy foot cause thee to stumble, cut it off: it is good for thee to enter into life halt, rather than having thy two feet to be cast into hell."
      },
      bible_name: 'web_american_standard'
    },
    github_commit_graph: {
      username: 'ryanckulp',
      instance_name: '@ryanckulp',
      contributions: {
        total: 920,
        commits: [
          { contributionDays:
              [
                { contributionCount: 0, date: "2023-10-22" },
                { contributionCount: 0, date: "2023-10-23" },
                { contributionCount: 4, date: "2023-10-24" },
                { contributionCount: 6, date: "2023-10-25" },
                { contributionCount: 3, date: "2023-10-26" },
                { contributionCount: 0, date: "2023-10-27" },
                { contributionCount: 0, date: "2023-10-28" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2023-10-29" },
                { contributionCount: 10, date: "2023-10-30" },
                { contributionCount: 2, date: "2023-10-31" },
                { contributionCount: 0, date: "2023-11-01" },
                { contributionCount: 0, date: "2023-11-02" },
                { contributionCount: 0, date: "2023-11-03" },
                { contributionCount: 0, date: "2023-11-04" }] },
          { contributionDays:
              [
                { contributionCount: 2, date: "2023-11-05" },
                { contributionCount: 7, date: "2023-11-06" },
                { contributionCount: 1, date: "2023-11-07" },
                { contributionCount: 7, date: "2023-11-08" },
                { contributionCount: 2, date: "2023-11-09" },
                { contributionCount: 4, date: "2023-11-10" },
                { contributionCount: 10, date: "2023-11-11" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 4, date: "2023-11-12" },
                { contributionCount: 1, date: "2023-11-13" },
                { contributionCount: 3, date: "2023-11-14" },
                { contributionCount: 8, date: "2023-11-15" },
                { contributionCount: 5, date: "2023-11-16" },
                { contributionCount: 2, date: "2023-11-17" },
                { contributionCount: 0, date: "2023-11-18" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2023-11-19" },
                { contributionCount: 2, date: "2023-11-20" },
                { contributionCount: 0, date: "2023-11-21" },
                { contributionCount: 0, date: "2023-11-22" },
                { contributionCount: 0, date: "2023-11-23" },
                { contributionCount: 0, date: "2023-11-24" },
                { contributionCount: 0, date: "2023-11-25" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2023-11-26" },
                { contributionCount: 4, date: "2023-11-27" },
                { contributionCount: 0, date: "2023-11-28" },
                { contributionCount: 0, date: "2023-11-29" },
                { contributionCount: 0, date: "2023-11-30" },
                { contributionCount: 2, date: "2023-12-01" },
                { contributionCount: 1, date: "2023-12-02" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 2, date: "2023-12-03" },
                { contributionCount: 1, date: "2023-12-04" },
                { contributionCount: 1, date: "2023-12-05" },
                { contributionCount: 0, date: "2023-12-06" },
                { contributionCount: 3, date: "2023-12-07" },
                { contributionCount: 0, date: "2023-12-08" },
                { contributionCount: 0, date: "2023-12-09" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 1, date: "2023-12-10" },
                { contributionCount: 1, date: "2023-12-11" },
                { contributionCount: 1, date: "2023-12-12" },
                { contributionCount: 0, date: "2023-12-13" },
                { contributionCount: 0, date: "2023-12-14" },
                { contributionCount: 1, date: "2023-12-15" },
                { contributionCount: 1, date: "2023-12-16" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2023-12-17" },
                { contributionCount: 0, date: "2023-12-18" },
                { contributionCount: 11, date: "2023-12-19" },
                { contributionCount: 1, date: "2023-12-20" },
                { contributionCount: 2, date: "2023-12-21" },
                { contributionCount: 0, date: "2023-12-22" },
                { contributionCount: 0, date: "2023-12-23" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2023-12-24" },
                { contributionCount: 0, date: "2023-12-25" },
                { contributionCount: 6, date: "2023-12-26" },
                { contributionCount: 5, date: "2023-12-27" },
                { contributionCount: 13, date: "2023-12-28" },
                { contributionCount: 1, date: "2023-12-29" },
                { contributionCount: 0, date: "2023-12-30" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 1, date: "2023-12-31" },
                { contributionCount: 0, date: "2024-01-01" },
                { contributionCount: 0, date: "2024-01-02" },
                { contributionCount: 2, date: "2024-01-03" },
                { contributionCount: 0, date: "2024-01-04" },
                { contributionCount: 0, date: "2024-01-05" },
                { contributionCount: 3, date: "2024-01-06" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 1, date: "2024-01-07" },
                { contributionCount: 3, date: "2024-01-08" },
                { contributionCount: 2, date: "2024-01-09" },
                { contributionCount: 21, date: "2024-01-10" },
                { contributionCount: 1, date: "2024-01-11" },
                { contributionCount: 0, date: "2024-01-12" },
                { contributionCount: 6, date: "2024-01-13" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 1, date: "2024-01-14" },
                { contributionCount: 4, date: "2024-01-15" },
                { contributionCount: 1, date: "2024-01-16" },
                { contributionCount: 3, date: "2024-01-17" },
                { contributionCount: 5, date: "2024-01-18" },
                { contributionCount: 0, date: "2024-01-19" },
                { contributionCount: 0, date: "2024-01-20" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 2, date: "2024-01-21" },
                { contributionCount: 0, date: "2024-01-22" },
                { contributionCount: 0, date: "2024-01-23" },
                { contributionCount: 4, date: "2024-01-24" },
                { contributionCount: 0, date: "2024-01-25" },
                { contributionCount: 0, date: "2024-01-26" },
                { contributionCount: 1, date: "2024-01-27" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-01-28" },
                { contributionCount: 9, date: "2024-01-29" },
                { contributionCount: 3, date: "2024-01-30" },
                { contributionCount: 3, date: "2024-01-31" },
                { contributionCount: 0, date: "2024-02-01" },
                { contributionCount: 0, date: "2024-02-02" },
                { contributionCount: 1, date: "2024-02-03" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-02-04" },
                { contributionCount: 0, date: "2024-02-05" },
                { contributionCount: 0, date: "2024-02-06" },
                { contributionCount: 0, date: "2024-02-07" },
                { contributionCount: 0, date: "2024-02-08" },
                { contributionCount: 0, date: "2024-02-09" },
                { contributionCount: 0, date: "2024-02-10" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-02-11" },
                { contributionCount: 1, date: "2024-02-12" },
                { contributionCount: 2, date: "2024-02-13" },
                { contributionCount: 0, date: "2024-02-14" },
                { contributionCount: 0, date: "2024-02-15" },
                { contributionCount: 2, date: "2024-02-16" },
                { contributionCount: 0, date: "2024-02-17" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 4, date: "2024-02-18" },
                { contributionCount: 7, date: "2024-02-19" },
                { contributionCount: 3, date: "2024-02-20" },
                { contributionCount: 1, date: "2024-02-21" },
                { contributionCount: 1, date: "2024-02-22" },
                { contributionCount: 3, date: "2024-02-23" },
                { contributionCount: 1, date: "2024-02-24" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 2, date: "2024-02-25" },
                { contributionCount: 5, date: "2024-02-26" },
                { contributionCount: 2, date: "2024-02-27" },
                { contributionCount: 4, date: "2024-02-28" },
                { contributionCount: 0, date: "2024-02-29" },
                { contributionCount: 1, date: "2024-03-01" },
                { contributionCount: 0, date: "2024-03-02" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 4, date: "2024-03-03" },
                { contributionCount: 0, date: "2024-03-04" },
                { contributionCount: 4, date: "2024-03-05" },
                { contributionCount: 5, date: "2024-03-06" },
                { contributionCount: 2, date: "2024-03-07" },
                { contributionCount: 0, date: "2024-03-08" },
                { contributionCount: 4, date: "2024-03-09" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 20, date: "2024-03-10" },
                { contributionCount: 0, date: "2024-03-11" },
                { contributionCount: 2, date: "2024-03-12" },
                { contributionCount: 2, date: "2024-03-13" },
                { contributionCount: 10, date: "2024-03-14" },
                { contributionCount: 0, date: "2024-03-15" },
                { contributionCount: 0, date: "2024-03-16" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 2, date: "2024-03-17" },
                { contributionCount: 5, date: "2024-03-18" },
                { contributionCount: 4, date: "2024-03-19" },
                { contributionCount: 3, date: "2024-03-20" },
                { contributionCount: 2, date: "2024-03-21" },
                { contributionCount: 0, date: "2024-03-22" },
                { contributionCount: 0, date: "2024-03-23" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-03-24" },
                { contributionCount: 1, date: "2024-03-25" },
                { contributionCount: 2, date: "2024-03-26" },
                { contributionCount: 4, date: "2024-03-27" },
                { contributionCount: 2, date: "2024-03-28" },
                { contributionCount: 1, date: "2024-03-29" },
                { contributionCount: 0, date: "2024-03-30" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 1, date: "2024-03-31" },
                { contributionCount: 0, date: "2024-04-01" },
                { contributionCount: 2, date: "2024-04-02" },
                { contributionCount: 22, date: "2024-04-03" },
                { contributionCount: 2, date: "2024-04-04" },
                { contributionCount: 1, date: "2024-04-05" },
                { contributionCount: 0, date: "2024-04-06" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-04-07" },
                { contributionCount: 6, date: "2024-04-08" },
                { contributionCount: 2, date: "2024-04-09" },
                { contributionCount: 6, date: "2024-04-10" },
                { contributionCount: 4, date: "2024-04-11" },
                { contributionCount: 2, date: "2024-04-12" },
                { contributionCount: 0, date: "2024-04-13" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-04-14" },
                { contributionCount: 3, date: "2024-04-15" },
                { contributionCount: 5, date: "2024-04-16" },
                { contributionCount: 5, date: "2024-04-17" },
                { contributionCount: 2, date: "2024-04-18" },
                { contributionCount: 0, date: "2024-04-19" },
                { contributionCount: 0, date: "2024-04-20" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-04-21" },
                { contributionCount: 0, date: "2024-04-22" },
                { contributionCount: 3, date: "2024-04-23" },
                { contributionCount: 3, date: "2024-04-24" },
                { contributionCount: 6, date: "2024-04-25" },
                { contributionCount: 5, date: "2024-04-26" },
                { contributionCount: 27, date: "2024-04-27" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 6, date: "2024-04-28" },
                { contributionCount: 10, date: "2024-04-29" },
                { contributionCount: 1, date: "2024-04-30" },
                { contributionCount: 7, date: "2024-05-01" },
                { contributionCount: 2, date: "2024-05-02" },
                { contributionCount: 0, date: "2024-05-03" },
                { contributionCount: 1, date: "2024-05-04" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-05-05" },
                { contributionCount: 0, date: "2024-05-06" },
                { contributionCount: 1, date: "2024-05-07" },
                { contributionCount: 1, date: "2024-05-08" },
                { contributionCount: 2, date: "2024-05-09" },
                { contributionCount: 0, date: "2024-05-10" },
                { contributionCount: 0, date: "2024-05-11" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-05-12" },
                { contributionCount: 7, date: "2024-05-13" },
                { contributionCount: 5, date: "2024-05-14" },
                { contributionCount: 0, date: "2024-05-15" },
                { contributionCount: 0, date: "2024-05-16" },
                { contributionCount: 0, date: "2024-05-17" },
                { contributionCount: 0, date: "2024-05-18" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 2, date: "2024-05-19" },
                { contributionCount: 1, date: "2024-05-20" },
                { contributionCount: 3, date: "2024-05-21" },
                { contributionCount: 8, date: "2024-05-22" },
                { contributionCount: 2, date: "2024-05-23" },
                { contributionCount: 2, date: "2024-05-24" },
                { contributionCount: 0, date: "2024-05-25" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-05-26" },
                { contributionCount: 0, date: "2024-05-27" },
                { contributionCount: 8, date: "2024-05-28" },
                { contributionCount: 7, date: "2024-05-29" },
                { contributionCount: 2, date: "2024-05-30" },
                { contributionCount: 0, date: "2024-05-31" },
                { contributionCount: 4, date: "2024-06-01" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-06-02" },
                { contributionCount: 0, date: "2024-06-03" },
                { contributionCount: 0, date: "2024-06-04" },
                { contributionCount: 0, date: "2024-06-05" },
                { contributionCount: 0, date: "2024-06-06" },
                { contributionCount: 0, date: "2024-06-07" },
                { contributionCount: 2, date: "2024-06-08" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-06-09" },
                { contributionCount: 0, date: "2024-06-10" },
                { contributionCount: 0, date: "2024-06-11" },
                { contributionCount: 0, date: "2024-06-12" },
                { contributionCount: 0, date: "2024-06-13" },
                { contributionCount: 4, date: "2024-06-14" },
                { contributionCount: 0, date: "2024-06-15" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-06-16" },
                { contributionCount: 0, date: "2024-06-17" },
                { contributionCount: 4, date: "2024-06-18" },
                { contributionCount: 5, date: "2024-06-19" },
                { contributionCount: 0, date: "2024-06-20" },
                { contributionCount: 3, date: "2024-06-21" },
                { contributionCount: 3, date: "2024-06-22" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 2, date: "2024-06-23" },
                { contributionCount: 2, date: "2024-06-24" },
                { contributionCount: 0, date: "2024-06-25" },
                { contributionCount: 1, date: "2024-06-26" },
                { contributionCount: 0, date: "2024-06-27" },
                { contributionCount: 0, date: "2024-06-28" },
                { contributionCount: 0, date: "2024-06-29" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 2, date: "2024-06-30" },
                { contributionCount: 4, date: "2024-07-01" },
                { contributionCount: 5, date: "2024-07-02" },
                { contributionCount: 4, date: "2024-07-03" },
                { contributionCount: 1, date: "2024-07-04" },
                { contributionCount: 2, date: "2024-07-05" },
                { contributionCount: 0, date: "2024-07-06" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 3, date: "2024-07-07" },
                { contributionCount: 5, date: "2024-07-08" },
                { contributionCount: 3, date: "2024-07-09" },
                { contributionCount: 3, date: "2024-07-10" },
                { contributionCount: 2, date: "2024-07-11" },
                { contributionCount: 2, date: "2024-07-12" },
                { contributionCount: 3, date: "2024-07-13" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-07-14" },
                { contributionCount: 6, date: "2024-07-15" },
                { contributionCount: 8, date: "2024-07-16" },
                { contributionCount: 3, date: "2024-07-17" },
                { contributionCount: 3, date: "2024-07-18" },
                { contributionCount: 8, date: "2024-07-19" },
                { contributionCount: 8, date: "2024-07-20" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 7, date: "2024-07-21" },
                { contributionCount: 1, date: "2024-07-22" },
                { contributionCount: 5, date: "2024-07-23" },
                { contributionCount: 4, date: "2024-07-24" },
                { contributionCount: 10, date: "2024-07-25" },
                { contributionCount: 2, date: "2024-07-26" },
                { contributionCount: 1, date: "2024-07-27" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 1, date: "2024-07-28" },
                { contributionCount: 2, date: "2024-07-29" },
                { contributionCount: 13, date: "2024-07-30" },
                { contributionCount: 3, date: "2024-07-31" },
                { contributionCount: 2, date: "2024-08-01" },
                { contributionCount: 3, date: "2024-08-02" },
                { contributionCount: 0, date: "2024-08-03" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-08-04" },
                { contributionCount: 2, date: "2024-08-05" },
                { contributionCount: 0, date: "2024-08-06" },
                { contributionCount: 5, date: "2024-08-07" },
                { contributionCount: 0, date: "2024-08-08" },
                { contributionCount: 0, date: "2024-08-09" },
                { contributionCount: 0, date: "2024-08-10" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 2, date: "2024-08-11" },
                { contributionCount: 3, date: "2024-08-12" },
                { contributionCount: 9, date: "2024-08-13" },
                { contributionCount: 0, date: "2024-08-14" },
                { contributionCount: 1, date: "2024-08-15" },
                { contributionCount: 5, date: "2024-08-16" },
                { contributionCount: 4, date: "2024-08-17" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-08-18" },
                { contributionCount: 6, date: "2024-08-19" },
                { contributionCount: 2, date: "2024-08-20" },
                { contributionCount: 5, date: "2024-08-21" },
                { contributionCount: 2, date: "2024-08-22" },
                { contributionCount: 0, date: "2024-08-23" },
                { contributionCount: 0, date: "2024-08-24" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-08-25" },
                { contributionCount: 3, date: "2024-08-26" },
                { contributionCount: 4, date: "2024-08-27" },
                { contributionCount: 5, date: "2024-08-28" },
                { contributionCount: 0, date: "2024-08-29" },
                { contributionCount: 1, date: "2024-08-30" },
                { contributionCount: 0, date: "2024-08-31" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-09-01" },
                { contributionCount: 0, date: "2024-09-02" },
                { contributionCount: 4, date: "2024-09-03" },
                { contributionCount: 14, date: "2024-09-04" },
                { contributionCount: 4, date: "2024-09-05" },
                { contributionCount: 3, date: "2024-09-06" },
                { contributionCount: 0, date: "2024-09-07" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 4, date: "2024-09-08" },
                { contributionCount: 2, date: "2024-09-09" },
                { contributionCount: 0, date: "2024-09-10" },
                { contributionCount: 0, date: "2024-09-11" },
                { contributionCount: 3, date: "2024-09-12" },
                { contributionCount: 0, date: "2024-09-13" },
                { contributionCount: 3, date: "2024-09-14" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 4, date: "2024-09-15" },
                { contributionCount: 3, date: "2024-09-16" },
                { contributionCount: 6, date: "2024-09-17" },
                { contributionCount: 14, date: "2024-09-18" },
                { contributionCount: 3, date: "2024-09-19" },
                { contributionCount: 8, date: "2024-09-20" },
                { contributionCount: 0, date: "2024-09-21" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 2, date: "2024-09-22" },
                { contributionCount: 4, date: "2024-09-23" },
                { contributionCount: 2, date: "2024-09-24" },
                { contributionCount: 0, date: "2024-09-25" },
                { contributionCount: 0, date: "2024-09-26" },
                { contributionCount: 0, date: "2024-09-27" },
                { contributionCount: 0, date: "2024-09-28" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 12, date: "2024-09-29" },
                { contributionCount: 8, date: "2024-09-30" },
                { contributionCount: 3, date: "2024-10-01" },
                { contributionCount: 4, date: "2024-10-02" },
                { contributionCount: 0, date: "2024-10-03" },
                { contributionCount: 1, date: "2024-10-04" },
                { contributionCount: 0, date: "2024-10-05" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 7, date: "2024-10-06" },
                { contributionCount: 3, date: "2024-10-07" },
                { contributionCount: 2, date: "2024-10-08" },
                { contributionCount: 13, date: "2024-10-09" },
                { contributionCount: 4, date: "2024-10-10" },
                { contributionCount: 2, date: "2024-10-11" },
                { contributionCount: 4, date: "2024-10-12" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-10-13" },
                { contributionCount: 3, date: "2024-10-14" },
                { contributionCount: 0, date: "2024-10-15" },
                { contributionCount: 4, date: "2024-10-16" },
                { contributionCount: 2, date: "2024-10-17" },
                { contributionCount: 2, date: "2024-10-18" },
                { contributionCount: 0, date: "2024-10-19" }
              ]
          },
          { contributionDays:
              [
                { contributionCount: 0, date: "2024-10-20" }
              ]
          }]
      },
      stats: {
        longest_streak: 19, current_streak: 3, max_contributions: 27, average_contributions: 2.45
      }
    },
    days_left_year: {},
    days_left_retirement: { percent: 47, weeks_left: 1672 },
    wiki_random_article: {
      article: {
        summary: "<p><i><b>First Time Around</b></i> is a one-disk DVD by Randy Bachman and Burton Cummings recorded in 2006 at CBC Studios in Toronto, Canada, by CBC. It was originally shown on CBC in April 2006, but was later released as a DVD with extended footage of the concert. The concert has 20 tracks of songs by Bachman-Turner Overdrive, Burton Cummings, The Guess Who and cover versions of artists such as Sting and Jimi Hendrix. It was originally shown on CBC in April 2006, but was later released as a DVD with extended footage of the concert. The concert has 20 tracks of songs by Bachman-Turner Overdrive, Burton Cummings, The Guess Who and cover versions of artists such as Sting and Jimi Hendrix Sting and Jimi Hendrix. It was originally shown on CBC in April 2006, but was later released as a DVD with extended footage of the concert. The concert has 20 tracks of songs by Bachman-Turner Overdrive, Burton Cummings, The Guess Who and cover versions of artists such as Sting and Jimi Hendrix Sting and Jimi Hendrix. It was originally shown on CBC in April 2006, but was later released as a DVD with extended footage of the concert. The concert has 20 tracks of songs by Bachman-Turner Overdrive, Burton Cummings, The Guess Who and cover versions of artists such as Sting and Jimi Hendrix.</p>",
        title: 'First Time Around'
      }
    },
    github_contributor_summary: {
      metrics: {
        repo: 'usetrmnl/core',
        contributors: [
          { total_commits: 527, handle: "ronakjain90", commits_last_week: 94 },
          { total_commits: 315, handle: "ryanckulp", commits_last_week: 43 },
          { total_commits: 279, handle: "loucyan", commits_last_week: 37 },
          { total_commits: 112, handle: "pooriajr", commits_last_week: 24 },
          { total_commits: 189, handle: "dependabot", commits_last_week: 12 },
          { total_commits: 142, handle: "github-actions[bot]", commits_last_week: 15 },
          { total_commits: 203, handle: "snyk-bot", commits_last_week: 18 },
          { total_commits: 134, handle: "codecov-io", commits_last_week: 10 },
          { total_commits: 98, handle: "codewizard42", commits_last_week: 22 },
          { total_commits: 76, handle: "bughunter007", commits_last_week: 19 },
          { total_commits: 54, handle: "refactor_ninja", commits_last_week: 14 },
          { total_commits: 45, handle: "devops_guru", commits_last_week: 11 },
          { total_commits: 32, handle: "frontend_fanatic", commits_last_week: 9 },
          { total_commits: 21, handle: "backend_beast", commits_last_week: 7 }
        ]
      }
    },
    shopify: {
      # dark_mode: true, # add to any demo data root node to test dark mode
      metrics: {
        open_order_count: 763, closed_order_count: 254, order_sales: 159022.14, aov: 156.36,
        # errors: 'Invalid Store Web Address or Access Token'
      },
      currency: '$', # €
      histogram: {
        current:
          [{ date: "2024-12-01", value: 946.02 },
           { date: "2024-12-02", value: 1441.82 },
           { date: "2024-12-03", value: 449.37 },
           { date: "2024-12-04", value: 452.0 },
           { date: "2024-12-05", value: 705.0 },
           { date: "2024-12-06", value: 936.43 },
           { date: "2024-12-07", value: 2681.07 },
           { date: "2024-12-08", value: 3770.49 },
           { date: "2024-12-09", value: 2260.71 },
           { date: "2024-12-10", value: 3609.26 },
           { date: "2024-12-11", value: 4116.29 },
           { date: "2024-12-12", value: 2447.15 },
           { date: "2024-12-13", value: 1180.39 },
           { date: "2024-12-14", value: 714.16 },
           { date: "2024-12-15", value: 1863.91 },
           { date: "2024-12-16", value: 1512.0 },
           { date: "2024-12-17", value: 867.28 },
           { date: "2024-12-18", value: 1673.34 },
           { date: "2024-12-19", value: 13877.3 },
           { date: "2024-12-20", value: 32002.05 },
           { date: "2024-12-21", value: 17434.28 },
           { date: "2024-12-22", value: 12187.9 },
           { date: "2024-12-23", value: 10076.88 },
           { date: "2024-12-24", value: 4633.01 },
           { date: "2024-12-25", value: 4358.82 },
           { date: "2024-12-26", value: 5784.35 },
           { date: "2024-12-27", value: 6120.38 },
           { date: "2024-12-28", value: 3170.55 },
           { date: "2024-12-29", value: 3004.26 },
           { date: "2024-12-30", value: 10763.44 },
           { date: "2024-12-31", value: 3982.23 }],
        comparison:
          [{ date: "2024-12-01", value: 425.28 },
           { date: "2024-12-03", value: 326.3 },
           { date: "2024-12-04", value: 462.96 },
           { date: "2024-12-05", value: 294.26 },
           { date: "2024-12-06", value: 751.98 },
           { date: "2024-12-08", value: 296.12 },
           { date: "2024-12-09", value: 131.78 },
           { date: "2024-12-10", value: 619.4 },
           { date: "2024-12-11", value: 616.0 },
           { date: "2024-12-12", value: 462.0 },
           { date: "2024-12-14", value: 9524.06 },
           { date: "2024-12-15", value: 7180.58 },
           { date: "2024-12-16", value: 2815.0 },
           { date: "2024-12-17", value: 2194.1 },
           { date: "2024-12-18", value: 909.08 },
           { date: "2024-12-19", value: 1499.78 },
           { date: "2024-12-20", value: 2029.41 },
           { date: "2024-12-21", value: 884.0 },
           { date: "2024-12-22", value: 972.72 },
           { date: "2024-12-23", value: 909.0 },
           { date: "2024-12-24", value: 151.99 },
           { date: "2024-12-25", value: 1824.98 },
           { date: "2024-12-26", value: 748.73 },
           { date: "2024-12-27", value: 1064.97 },
           { date: "2024-12-28", value: 772.56 },
           { date: "2024-12-29", value: 1478.86 },
           { date: "2024-12-30", value: 590.56 }] },
      comparison_period: ["2024-11-01", "2024-11-30"],
      instance_name: 'usetrmnl.com'
    },
    coinmarketcap: {
      currency_symbol: '$', # $, ₩, €, £ etc
      currency_separator: ',',
      tickers: [
        {
          symbol: 'BTC',
          price: '69420.99',
          change: '+4.62%',
          name: 'Bitcoin'
        },
        {
          symbol: 'ETH',
          price: '1234.56',
          change: '+16.43%',
          name: 'Ethereum'
        },
        {
          symbol: 'SHIB',
          price: '2.7e-05',
          change: '+16.43%',
          name: 'Shiba Inu'
        }
      ]
    },
    stock_price: {
      currency_symbol: '€', # $, ₩, £ etc
      currency_separator: ',',
      # currency_delimiter: '.', # IDEA: not implemented
      tickers: [
        {
          symbol: 'AAPL',
          price: '226.40',
          change: '+0.62%',
          name: 'Apple Inc.'
        },
        {
          symbol: 'SPY',
          price: '560.62',
          change: '+16.43%',
          name: 'S&P 500 Index'
        },
      # {
      #   symbol: 'NVDA',
      #   price: '128.50',
      #   change: '-2.87%',
      #   name: 'NVIDIA Corporation'
      # },
      # {
      #   symbol: 'TSLA',
      #   price: '229.01',
      #   change: '+5.16%',
      #   name: 'Tesla, Inc.'
      # },
      # {
      #   symbol: 'AMZN',
      #   price: '180.11',
      #   change: '+1.24%',
      #   name: 'Amazon.com Inc.'
      # },
      # {
      #   symbol: 'GOOG',
      #   price: '165.85',
      #   change: '-0.78%',
      #   name: 'Alphabet Inc.'
      # },
      # {
      #   symbol: 'MSFT',
      #   price: '424.14',
      #   change: '+0.95%',
      #   name: 'Microsoft Corporation'
      # },
      # {
      #   symbol: 'META',
      #   price: '535.16',
      #   change: '-1.15%',
      #   name: 'Meta Platforms, Inc.'
      # },
      # {
      #   symbol: 'NFLX',
      #   price: '697.12',
      #   change: '+2.57%',
      #   name: 'Netflix, Inc.'
      # },
      # {
      #   symbol: 'JPM',
      #   price: '214.60',
      #   change: '+0.33%',
      #   name: 'JPMorgan Chase & Co.'
      # },
      # {
      #   symbol: 'DIS',
      #   price: '175.30',
      #   change: '-0.45%',
      #   name: 'The Walt Disney Company'
      # },
      # {
      #   symbol: 'V',
      #   price: '220.25',
      #   change: '+1.10%',
      #   name: 'Visa Inc.'
      # }
      ]
    },
    google_calendar: {
      event_layout: 'schedule', # available: default, today_only, week, month, rolling_month
      zoom_mode: false,
      include_description: true, # available: true, false
      include_event_time: true, # available: true, false (schedule, month view)
      first_day: 1, # Sunday=0, Monday=1, etc
      fixed_week: true,
      scroll_time: '07:30',
      scroll_time_end: '21:00',
      time_format: 'am/pm', # 'am/pm' or '24_hrs'
      date_format: '%a, %b %-d', # Thu, Nov 26
      instance_name: 'Gmail Personal',
      events: calendar_events + past_calendar_events, # include past events to showcase fixed_week: true
      today_in_tz: DateTime.now.in_time_zone('America/New_York').beginning_of_day
    },
    outlook_calendar: {
      event_layout: 'week', # available: default, today_only, week, month, rolling_month
      include_description: true, # available: true, false
      include_event_time: false, # available: true, false (month view only)
      first_day: 1, # Sunday=0, Monday=1, etc
      fixed_week: false,
      scroll_time: '08:00',
      scroll_time_end: '21:00',
      time_format: '24_hrs', # 'am/pm' or '24_hrs'
      date_format: '%a, %b %-d', # Thu, Nov 26
      instance_name: 'Outlook Personal',
      events: calendar_events,
      today_in_tz: DateTime.now.in_time_zone('America/New_York').beginning_of_day
    },
    apple_calendar: {
      event_layout: 'rolling_month', # available: default, today_only, week, month, rolling_month
      include_description: true, # available: true, false
      include_event_time: true, # available: true, false (month view only)
      first_day: 1, # Sunday=0, Monday=1, etc
      fixed_week: false,
      scroll_time: '11:00',
      scroll_time_end: '21:00',
      time_format: '24_hrs', # 'am/pm' or '24_hrs'
      date_format: '%a, %b %-d', # Thu, Nov 26
      instance_name: 'Apple Personal',
      events: calendar_events,
      today_in_tz: DateTime.now.in_time_zone('America/New_York').beginning_of_day
    },
    caldav: {
      event_layout: 'week', # available: default, today_only, week, month, rolling_month
      include_description: true, # available: true, false
      include_event_time: false, # available: true, false (month view only)
      first_day: 1, # Sunday=0, Monday=1, etc
      fixed_week: false,
      scroll_time: '11:00',
      scroll_time_end: '21:00',
      time_format: 'am/pm', # 'am/pm' or '24_hrs'
      date_format: '%a, %b %-d', # Thu, Nov 26
      instance_name: 'Custom Calendar',
      events: calendar_events,
      today_in_tz: DateTime.now.in_time_zone('America/New_York').beginning_of_day
    },
    fastmail_calendar: {
      event_layout: 'default', # available: default, today_only, week, month, rolling_month
      include_description: true, # available: true, false
      include_event_time: false, # available: true, false (month view only)
      first_day: 1, # Sunday=0, Monday=1, etc
      fixed_week: false,
      scroll_time: '11:00',
      scroll_time_end: '21:00',
      time_format: 'am/pm', # 'am/pm' or '24_hrs'
      date_format: '%a, %b %-d', # Thu, Nov 26
      instance_name: 'Fastmail Personal',
      events: calendar_events,
      today_in_tz: DateTime.now.in_time_zone('America/New_York').beginning_of_day
    },
    nextcloud_calendar: {
      event_layout: 'week', # available: default, today_only, week, month, rolling_month
      include_description: true, # available: true, false
      include_event_time: false, # available: true, false (month view only)
      first_day: 1, # Sunday=0, Monday=1, etc
      fixed_week: false,
      scroll_time: '11:00',
      scroll_time_end: '21:00',
      time_format: 'am/pm', # 'am/pm' or '24_hrs'
      date_format: '%a, %b %-d', # Thu, Nov 26
      instance_name: 'Nextcloud Personal',
      events: calendar_events,
      today_in_tz: DateTime.now.in_time_zone('America/New_York').beginning_of_day
    },
    notion: {
      variants: {
        database: {
          items: [
            {
              title: "Website Redesign",
              properties: [
                { name: "Status", type: "status", value: "In Progress" },
                { name: "Priority", type: "select", value: "High" },
                { name: "Due Date", type: "date", value: "Jan 15, 2025" },
                { name: "Team", type: "multi_select", value: "Design, Frontend" },
                { name: "Progress", type: "number", value: 65 },
                { name: "Owner", type: "people", value: "Sarah Chen" }
              ],
              url: "https://notion.so/website-redesign",
              last_edited: "Jan 10, 2025"
            },
            {
              title: "Mobile App Development",
              properties: [
                { name: "Status", type: "status", value: "Planning" },
                { name: "Priority", type: "select", value: "High" },
                { name: "Due Date", type: "date", value: "Mar 1, 2025" },
                { name: "Team", type: "multi_select", value: "Mobile, Backend" },
                { name: "Progress", type: "number", value: 15 },
                { name: "Owner", type: "people", value: "Alex Kim" }
              ],
              url: "https://notion.so/mobile-app",
              last_edited: "Jan 9, 2025"
            },
            {
              title: "API Documentation Update",
              properties: [
                { name: "Status", type: "status", value: "Done" },
                { name: "Priority", type: "select", value: "Medium" },
                { name: "Due Date", type: "date", value: "Dec 20, 2024" },
                { name: "Team", type: "multi_select", value: "Documentation" },
                { name: "Progress", type: "number", value: 100 },
                { name: "Owner", type: "people", value: "Mike Johnson" }
              ],
              url: "https://notion.so/api-docs",
              last_edited: "Dec 20, 2024"
            },
            {
              title: "User Authentication System",
              properties: [
                { name: "Status", type: "status", value: "In Progress" },
                { name: "Priority", type: "select", value: "Critical" },
                { name: "Due Date", type: "date", value: "Jan 20, 2025" },
                { name: "Team", type: "multi_select", value: "Backend, Security" },
                { name: "Progress", type: "number", value: 40 },
                { name: "Owner", type: "people", value: "Emma Wilson" }
              ],
              url: "https://notion.so/auth-system",
              last_edited: "Jan 11, 2025"
            },
            {
              title: "Performance Optimization",
              properties: [
                { name: "Status", type: "status", value: "Review" },
                { name: "Priority", type: "select", value: "Medium" },
                { name: "Due Date", type: "date", value: "Feb 5, 2025" },
                { name: "Team", type: "multi_select", value: "DevOps, Backend" },
                { name: "Progress", type: "number", value: 80 },
                { name: "Owner", type: "people", value: "David Liu" }
              ],
              url: "https://notion.so/performance-opt",
              last_edited: "Jan 8, 2025"
            },
            {
              title: "Website Redesign",
              properties: [
                { name: "Status", type: "status", value: "In Progress" },
                { name: "Priority", type: "select", value: "High" },
                { name: "Due Date", type: "date", value: "Jan 15, 2025" },
                { name: "Team", type: "multi_select", value: "Design, Frontend" },
                { name: "Progress", type: "number", value: 65 },
                { name: "Owner", type: "people", value: "Sarah Chen" }
              ],
              url: "https://notion.so/website-redesign",
              last_edited: "Jan 10, 2025"
            },
            {
              title: "Mobile App Development",
              properties: [
                { name: "Status", type: "status", value: "Planning" },
                { name: "Priority", type: "select", value: "High" },
                { name: "Due Date", type: "date", value: "Mar 1, 2025" },
                { name: "Team", type: "multi_select", value: "Mobile, Backend" },
                { name: "Progress", type: "number", value: 15 },
                { name: "Owner", type: "people", value: "Alex Kim" }
              ],
              url: "https://notion.so/mobile-app",
              last_edited: "Jan 9, 2025"
            },
          ],
          display_type: "database",
          multi_column_display: "1",
          instance_name: "Project Dashboard",
          status_field: "Status",
          labeled_properties: ["Priority", "Team"],
          listed_properties: ["Progress", "Owner", "Due Date"]
        },
        page: {
          items: {
            blocks: [
              { type: "image", image_url: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=400", caption: "Annual company retreat 2024" },
              { type: "heading_1", text: "Company Wiki & Knowledge Base" },
              { type: "paragraph", text: "Welcome to our comprehensive knowledge repository. Here you'll find everything you need to know about our processes, tools, culture, and best practices." },
              
              { type: "heading_2", text: "Quick Start" },
              { type: "bulleted_list_item", text: "New Employee Onboarding Checklist" },
              { type: "numbered_list_item", text: "Complete HR paperwork and setup accounts" },
              { type: "bulleted_list_item", text: "Development Environment Setup Guide" },
              
              { type: "heading_3", text: "Documentation Resources" },
              { type: "paragraph", text: "Our technical and process documentation is organized by department and function." },
              { type: "child_page", text: "Engineering Guidelines & Standards" },
              { type: "bookmark", text: "Company Handbook - https://company.notion.site/handbook" },
              
              { type: "divider" },
              
              { type: "heading_2", text: "Recent Updates" },
              { type: "callout", text: "New security training is now mandatory for all team members. Complete by January 31, 2025." },
              { type: "paragraph", text: "The engineering team has rolled out updated deployment procedures. All developers should review the new CI/CD workflow documentation." },
              
              { type: "code", text: "npm run deploy --env=production", language: "bash" },
              
              { type: "heading_2", text: "Action Items" },
              { type: "to_do", checked: true, text: "Q4 Performance Reviews - Completed December 2024" },
              { type: "to_do", checked: false, text: "Annual Security Training - Due January 31, 2025" },
              
              { type: "quote", text: "The best way to find out if you can trust somebody is to trust them. - Ernest Hemingway" },
              
              
              { type: "child_database", text: "Project Tracker Database" },
              
              { type: "equation", text: "E = mc^2" }
            ],
            properties: []
          },
          display_type: "page",
          multi_column_display: "2",
          image_height: 120,
          instance_name: "Company Wiki"
        }
      }
    },
    product_hunt: {
      featured_posts:
        [
          { "node" => {
            "id" => "433430",
            "name" => "Potis AI",
            "tagline" => "Your hiring copilot that tests the real skills",
            "slug" => "potis-ai",
            "votesCount" => 535,
            "commentsCount" => 84
          }
          },
          { "node" => {
            "id" => "433050",
            "name" => "Fliz AI",
            "tagline" => "Turn any URL into a stunning video with AI",
            "slug" => "fliz-ai",
            "votesCount" => 422,
            "commentsCount" => 149
          }
          },
          { "node" =>
              {
                "id" => "432225",
                "name" => "Seemless",
                "tagline" => "From social to site with zero disruption",
                "slug" => "seemless",
                "votesCount" => 302,
                "commentsCount" => 132
              } },
          { "node" =>
              {
                "id" => "432791",
                "name" => "Rabbit r1",
                "tagline" => "AI powered pocket companion",
                "slug" => "rabbit-r1",
                "votesCount" => 294,
                "commentsCount" => 49
              } },
          { "node" =>
              {
                "id" => "433670",
                "name" => "Clear 2.0",
                "tagline" => "Your life in lists",
                "slug" => "clear-2-0",
                "votesCount" => 219,
                "commentsCount" => 43
              } },
          { "node" =>
              {
                "id" => "433645",
                "name" => "Rimo",
                "tagline" => "Top TTS converter & audio editor to speed up your workflow",
                "slug" => "rimo",
                "votesCount" => 196,
                "commentsCount" => 61
              } },
          { "node" =>
              {
                "id" => "433748",
                "name" => "Startup Ratings",
                "tagline" => "A guide to the top companies to work at today",
                "slug" => "startup-ratings",
                "votesCount" => 180,
                "commentsCount" => 36
              } },
          { "node" =>
              {
                "id" => "429172",
                "name" => "Syllaby",
                "tagline" => "Use ChatGPT to summarize YouTube videos with Syllaby",
                "slug" => "syllaby-2",
                "votesCount" => 161,
                "commentsCount" => 71
              } },
          { "node" =>
              {
                "id" => "433739",
                "name" => "Starlink Direct to Cell",
                "tagline" => "Top TTS converter & audio editor to speed up your workflow",
                "slug" => "starlink-direct-to-cell",
                "votesCount" => 140,
                "commentsCount" => 34
              } },
          { "node" =>
              {
                "id" => "397060",
                "name" => "Klart AI",
                "tagline" => "AI assistant for everyone in your organization",
                "slug" => "klart-ai",
                "votesCount" => 95,
                "commentsCount" => 35
              } },
          { "node" =>
              {
                "id" => "433688",
                "name" => "Codara",
                "tagline" => "AI code review and AI code diagnostics",
                "slug" => "codara",
                "votesCount" => 88,
                "commentsCount" => 40
              } },
          { "node" =>
              {
                "id" => "433728",
                "name" => "Journal.",
                "tagline" => "A social journal with daily prompts",
                "slug" => "journal-8",
                "votesCount" => 67,
                "commentsCount" => 20
              } },
          { "node" =>
              {
                "id" => "433697",
                "name" => "Addio",
                "tagline" => "Create an estate plan over your morning coffee",
                "slug" => "addio",
                "votesCount" => 67,
                "commentsCount" => 22
              } },
          { "node" =>
              {
                "id" => "432935",
                "name" => "Brill",
                "tagline" => "Your AI powered productivity platform",
                "slug" => "brill-c257a2a0-f70e-455d-a5ca-a6a35959354c",
                "votesCount" => 60,
                "commentsCount" => 19
              } },
          { "node" =>
              {
                "id" => "433687",
                "name" => "Sleepmi Z3",
                "tagline" => "The smart anti-snoring device",
                "slug" => "sleepmi-z3",
                "votesCount" => 49,
                "commentsCount" => 9
              } },
          { "node" =>
              {
                "id" => "429842",
                "name" => "miflow®",
                "tagline" => "Holistic solution for boost efficiency and profitability",
                "slug" => "miflow-2",
                "votesCount" => 49,
                "commentsCount" => 14
              } },
          { "node" =>
              {
                "id" => "433676",
                "name" => "Auria Mail",
                "tagline" => "Like having your own email marketing team",
                "slug" => "auria-mail",
                "votesCount" => 47,
                "commentsCount" => 11
              } },
          { "node" =>
              {
                "id" => "433437",
                "name" => "FullEnrich",
                "tagline" => "Waterfall enrichment email & phone, all vendors, one place",
                "slug" => "fullenrich",
                "votesCount" => 1059,
                "commentsCount" => 284
              } },
          { "node" =>
              {
                "id" => "430909",
                "name" => "Rainex",
                "tagline" => "Your ideal billing and subscription management system",
                "slug" => "rainex",
                "votesCount" => 815,
                "commentsCount" => 284
              } },
          { "node" =>
              {
                "id" => "433560",
                "name" => "Sama AI",
                "tagline" => "Wearable AI mentor that proactively helps you become better",
                "slug" => "sama-ai",
                "votesCount" => 418,
                "commentsCount" => 14
              } }
        ].map(&:with_indifferent_access)
    },
    reddit: {
      featured_posts: [{ subreddit: "wallstreetbets",
                         posts:
                           [{ title: "Insights &amp; Snap Inc: Analyzing Market Trends, Investment Opportunities, and Predictive Growth Scenarios for 2024",
                              author: "traderInsight",
                              votes: 0 },
                            { title: "Anticipating Market Shifts: Strategies for the Upcoming Year",
                              author: "futurePredictor",
                              votes: 1 },
                            { title: "Dreaming of Gains: Strategies for Long-Term Success",
                              author: "longTermDreamer",
                              votes: 1 },
                            { title: "Exploring Short-term Trading Tactics",
                              author: "tacticalTrader",
                              votes: 0 },
                            { title: "Navigating Through Financial Setbacks: Overcoming Challenges",
                              author: "setbackSurvivor",
                              votes: 30 },
                            { title: "Unpacking the Impact of Federal Reserve Policies on Stock Market Volatility",
                              author: "policyWatcher",
                              votes: 15 },
                            { title: "The Future of Meme Stocks",
                              author: "memeMaster",
                              votes: 5 },
                            { title: "Options Trading Strategies for Beginners",
                              author: "optionGuru",
                              votes: 10 },
                            { title: "Analyzing the Impact of Inflation on Stock Prices",
                              author: "inflationWatcher",
                              votes: 8 },
                            { title: "Top 10 Stocks to Watch in 2024",
                              author: "stockSleuth",
                              votes: 12 },
                            { title: "Understanding Market Corrections",
                              author: "marketAnalyst",
                              votes: 7 },
                            { title: "The Role of AI in Stock Trading",
                              author: "aiTrader",
                              votes: 9 }] },
                       { subreddit: "buttcoin",
                         posts:
                           [{ title: "Bitcoin's Uncertain Horizon: Perspectives on Volatility and Regulation",
                              author: "skepticViewer",
                              votes: 23 },
                            { title: "Cryptocurrency Myths and Realities",
                              author: "mythBusterCrypto",
                              votes: 6 },
                            { title: "The 'Digital Gold' Debate: Analyzing Bitcoin as a Safe Haven Asset",
                              author: "digitalGoldSkeptic",
                              votes: 7 },
                            { title: "Deciphering Crypto Through Data",
                              author: "dataDrivenAnalyst",
                              votes: 36 },
                            { title: "Broadening Perspectives: The Long View on Crypto Adoption",
                              author: "wideAngleCrypto",
                              votes: 33 },
                            { title: "Exploring the Rise and Fall of Altcoins: Lessons and Future Predictions",
                              author: "altCoinAnalyst",
                              votes: 12 },
                            { title: "The Future of DeFi",
                              author: "defiExpert",
                              votes: 18 },
                            { title: "Crypto Security Best Practices",
                              author: "securityGuru",
                              votes: 20 },
                            { title: "NFTs: Fad or Future?",
                              author: "nftNerd",
                              votes: 14 },
                            { title: "The Impact of Government Regulations on Crypto",
                              author: "regulationWatcher",
                              votes: 22 },
                            { title: "Crypto Market Predictions for 2024",
                              author: "cryptoOracle",
                              votes: 19 },
                            { title: "Understanding Blockchain Technology",
                              author: "blockchainBuff",
                              votes: 25 }] }],
      sort_by: 'new',
      truncate_title: false,
      include_metadata: true
    },
    profit_well: {
      instance_name: 'Fomo.com',
      metrics: {
        new_recurring_revenue: 599,
        churned_recurring_revenue: 199,
        recurring_revenue: 419924,
        new_customers: 2,
        churned_customers: 1,
        active_customers: 111
      }
    },
    robinhood_portfolio: {
      instance_name: 'Personal',
      investments: [
        { name: "Stock Portfolio", balance: 43205.0 },
        { name: "Crypto", balance: 13631.98 }
      ],
      value_chart: randomized_robinhood_data
    },
    hacker_news: {
      instance_name: 'Show HN',
      stories:
        [
          { "title": "Hacker &amp; Bot", "score": 3, "by": "jibladze" },
          { "title": "Spice.ai – materialize, accelerate, and query SQL data from any source", "score": 149, "by": "lukekim" },
          { "title": "I built a web app to open source travel itineraries", "score": 134, "by": "onounoko" },
          { "title": "TechPeeker – Unveil Any Website's Tech Stack Instantly", "score": 5, "by": "Plutendo" },
          { "title": "An adventure game in Z80 assembly for CP/M and ZX Spectrum", "score": 4, "by": "stevekemp" },
          { "title": "I made a binary enigma machine for manual encryption", "score": 73, "by": "chjh" },
          { "title": "Seamless PWA with Zola", "score": 4, "by": "charlesrocket" },
          { "title": "A (marginally) useful x86-64 ELF executable in 466 bytes", "score": 82, "by": "meribold" },
          { "title": "I made a set of devtools for small projects", "score": 25, "by": "tinchox5" },
          { "title": "Pagecord – Effortless blogging from your inbox", "score": 12, "by": "lylo" },
          { "title": "I built an interactive plotter art exhibit for SIGGRAPH", "score": 163, "by": "cosiiine" },
          { "title": "Revolutionizing Note-Taking: An AI-powered text summarizer tool", "score": 46, "by": "edit_this" },
          { "title": "Exploring the Depths of Graph Databases with OctoGraphDB", "score": 21, "by": "neo4j_fan" },
          { "title": "MusicBox – A New Dimension in Music Streaming with Collaborative Playlists", "score": 8, "by": "melodist" },
          { "title": "Building a Custom Mechanical Keyboard", "score": 55, "by": "keyboardenthusiast" },
          { "title": "Understanding Quantum Computing", "score": 98, "by": "quantumgeek" },
          { "title": "A Guide to Rust Programming", "score": 120, "by": "rustacean" },
          { "title": "Creating a Personal Blog with Jekyll", "score": 30, "by": "blogger123" },
          { "title": "The Future of AI in Healthcare", "score": 200, "by": "aihealth" },
          { "title": "How to Build a Neural Network from Scratch", "score": 75, "by": "datascientist" }
        ],
      category: 'Show HN'
    },
    ethereum_wallet_balance: {
      ether: {
        account: "******************************************",
        balance: {
          usd: 165000,
          ether: 12.43,
        },
        change_7d: 9.23,
        change_90d: -20.3
      }
    },
    bitcoin_wallet_balance: {
      bitcoin: {
        account: "******************************************",
        balance: {
          usd: 6181610,
          bitcoin: 9.0641,
        },
        change_7d: 9.23,
        change_90d: -20.3
      }
    },
    youtube_analytics: {
      instance_name: '@MrBeast',
      metrics: {
        views: 120826,
        minutes_watched: 259277,
        average_view_duration: 129,
        average_view_percentage: 0.73,
        subscribers_gained: 789,
        likes: 18567,
        dislikes: 158,
        comments: 4568,
        shares: 2987
      }
    },
    mailchimp_analytics: {
      instance_name: 'My Company',
      metrics: {
        recent_activity: {
          email_sent: 4827,
          unique_opens: 3279,
          recipient_clicks: 658,
          hard_bounce: 23,
          soft_bounce: 89,
          subs: 143,
          unsubs: 37,
          open_rate: 67.9
        },
        growth_history: [
          { :date => "2024-12", :subscribes => 12113 },
          { :date => "2024-11", :subscribes => 12312 },
          { :date => "2024-10", :subscribes => 5132 },
          { :date => "2024-09", :subscribes => 2135 },
          { :date => "2024-08", :subscribes => 1000 },
          { :date => "2024-07", :subscribes => 900 },
          { :date => "2024-06", :subscribes => 550 },
          { :date => "2024-05", :subscribes => 945 },
          { :date => "2024-04", :subscribes => 1241 },
          { :date => "2024-03", :subscribes => 199 },
          { :date => "2024-02", :subscribes => 9 },
          { :date => "2024-01", :subscribes => 1 }
        ]
      }
    },
    convertkit_analytics: {
      instance_name: "Dan's Newsletter",
      metrics: {
        account_kpi: {
          purchase_count: 137,
          subscriber_count: 32117
        },
        broadcasts: {
          subject: "Pickleball Body Shots: How To Defend Against the Body Bag",
          recipients: 20129,
          open_rate: 6.***************,
          click_rate: 0.****************,
          unsubscribes: 14,
          total_clicks: 654,
          status: "completed"
        }
      }
    },
    teachable_analytics: {

    },
    screensaver: {
      image_url: Dir.glob("#{Rails.root}/public/images/screensaver/*.bmp").sample.split('public')[1]
    },
    kickstarter: {
      top_projects: [
        { name: "Altered TCG", description: "Explore the unexpected. For fans of trading card games, Altered could very well be a dream come true.", country: "France", pledged_amount: 2400746, percent_funded: 4427 },
        { name: "Animating Cradle: Bestselling Fantasy Novels Come to Life!", description: "Creating an animated adaptation of the Cradle series by Will Wight, partnering with legendary filmmaker Jay Oliva.", country: "the United States", pledged_amount: 1125896, percent_funded: 112 },
        { name: "YUKA 3D Vision Robot Lawn Sweeping Mower", description: "Self-emptying Tech | No Perimeter Wire | 3D Vision Navigation | Mow Under Trees丨Multi-zone Management丨bstacle Avoidance丨GPS Tracking", country: "Hong Kong", pledged_amount: 937523, percent_funded: 187 },
        { name: "YASHICA Vision binocular night vision: Capture Night in 4K", description: "YASHICA Vision is a binocular night vision device offering 4K image quality, full-color vision and up to 600m view range in darkness", country: "Hong Kong", pledged_amount: 703651, percent_funded: 3434 },
        { name: "KUKU Maker: Take Control Of Your Coffee Taste", description: "Enjoy your preferred coffee exactly the way you like it - with options for flavor, thick or light consistency, and hot or cold serving.", country: "Hong Kong", pledged_amount: 691850, percent_funded: 5409 },
        { name: "Rainy 75 Keyboard | Redefining $90-ish Mechanical Keyboards", description: "Magnificent Type Sounds| 2.4G+BT 5.0 | Wireless & Wired VIA Support | Full Aluminum | 7000mAh Battery | 10+ Choices | JWK Switches Incl", country: "the United States", pledged_amount: 659521, percent_funded: 6595 },
        { name: "EcoRide Urban Scooter", description: "Revolutionary electric scooter with solar charging panels and app-based navigation tools. Ideal for eco-friendly city commuting.", country: "Germany", pledged_amount: 1457822, percent_funded: 2437 },
        { name: "The Mindful Chef: Smart Kitchen Hub", description: "Intelligent cooking assistant featuring AI recipes, diet tracking, and automated grocery ordering to simplify your meal planning.", country: "Canada", pledged_amount: 865423, percent_funded: 1730 },
        { name: "Lumina: Dynamic Home Lighting", description: "Transform your living space with Lumina's adjustable and programmable lighting ecosystems, controlled via mobile or voice.", country: "Sweden", pledged_amount: 752991, percent_funded: 1506 },
        { name: "SkyHigh Drone Photography Kit", description: "Elevate your photography with our drone kit that includes 4K HDR capabilities, real-time editing, and cloud storage solutions.", country: "Australia", pledged_amount: 430859, percent_funded: 861 },
        { name: "Smart Garden", description: "Automated gardening system with AI-driven plant care and weather monitoring.", country: "Netherlands", pledged_amount: 500000, percent_funded: 1000 },
        { name: "Portable Solar Charger", description: "Compact and efficient solar charger for all your mobile devices.", country: "Japan", pledged_amount: 300000, percent_funded: 1500 },
        { name: "Eco-Friendly Water Bottle", description: "Reusable water bottle made from sustainable materials with built-in filter.", country: "Brazil", pledged_amount: 200000, percent_funded: 2000 }
      ],
      category: 'Ending Soon'
    },
    poetry_today: {
      poetry: {
        url: "jacob-bigelow/diccora-dogium-34967",
        title: "Ecclesiastical Sonnet Part III",
        author: "William Wordsworth",
        content: "The hero of\nAffairs of love\nBy far too numerous to be mentioned,\nAnd scarred as I'm,\nIt seemeth time\nThat I were mustered out and pensioned.\n<br />So on this wall\nMy lute and all\nI hang, and dedicate to Venus;\nAnd I implore\nBut one thing more\nEre all is at an end between us.\n<br />O goddess fair\nWho reignest where\nThe weather's seldom bleak and snowy,\nThis boon I urge:\nIn anger scourge\nMy old cantankerous sweetheart, Chloe!"
      }
    },
    rss_feed: {
      # rss_feed: [{
      #   title: "Learner Beware: to Know is to Care",
      #   content: "\n<p>i&#8217;ve developed a habit of resisting the urge to look up things i don&#8217;t know. and not just random facts like an actor&#8217;s age or pop culture reference. useful stuff, too. </p>\n\n\n\n<p>on 1 hand i&#8217;m always learning, studying, thinking. on the other i challenge myself to survive <em>without</em> technical terms and best practices. this applies to professional and casual tasks, like coding or messing with farm gear.</p>\n\n\n\n<p>when asked why i&#8217;m like this, i usually provide 1 of 3 reasons pending context.</p>\n\n\n\n<ol>\n<li>It (The Information) doesn&#8217;t matter</li>\n\n\n\n<li>&#8220;<em>Don&#8217;t correct people at a dinner party</em>&#8221; (Carnegie)</li>\n\n\n\n<li>Many people suffer from addiction to fact checking even the most insignificant details of a conversation and i&#8217;m trying to avoid this disease</li>\n</ol>\n\n\n\n<h3 class=\"wp-block-heading\">objections</h3>\n\n\n\n<p>#3 is how you start a fight. try mocking a friend who looks up bullsh*t factoids while you&#8217;re hanging out. they will defend themselves &#8211; &#8220;<em>i just enjoy knowing this stuff</em>.&#8221; ah yes, and the alcohlic simply loves whiskey.</p>\n\n\n\n<p>nobody seems to disagree with #2, except to note that most of life isn&#8217;t a social gathering. most of the time nobody is watching you, nobody cares about you, and nobody thinks you&#8217;re being rude if you whip out a phone. it&#8217;s totally normal.</p>\n\n\n\n<p>where i get the most kickback is #1. who&#8217;s to say &#8220;It&#8221; doesn&#8217;t matter? and besides, humans can store near infintite information. so knowing random stuff makes us interesting. right?</p>\n\n\n\n<h3 class=\"wp-block-heading\">we learn what we care about</h3>\n\n\n\n<p>before buying a ranch i didn&#8217;t know the difference between a bull vs steer, heifer vs cow. i didn&#8217;t know because i didn&#8217;t care. but suddenly i owned a few, so i learned.</p>\n\n\n\n<p>in 2021 i wanted to understand the crypto hype so i learned Solidity, built dozens of smart contracts, started a Web3 agency in Korea (cringe), and sold out multiple NFTs. i learned because i cared.</p>\n\n\n\n<p>but this is how information is acquired in a rational, efficient universe. <em>our</em> universe is inverted.</p>\n\n\n\n<h3 class=\"wp-block-heading\">we care about what we learn</h3>\n\n\n\n<p>some days everything is going fine until you hear about a school shooting. you <em>didn&#8217;t</em> know any of this, you <em>didn&#8217;t</em> care, but <span style=\"text-decoration: underline;\">now you do</span>.</p>\n\n\n\n<p>this rule touches everything, from political news to family drama and small town crime.</p>\n\n\n\n<p>now let&#8217;s suppose a best case scenario. that these facts enter your brain and are organized in limitless storage bins. it sounds great until we acknowledge the hidden cost. i codify this as Care.</p>\n\n\n\n<p>if you tell me Tom Cruise is only 5 foot 5 inches, i start to wonder how they make him look normal height in scenes with bigger actors. then i care about this movie magic effect. and by the way how tall is that chick who plays his wife? today&#8217;s Care: deducted by 1 point.</p>\n\n\n\n<h3 class=\"wp-block-heading\">infinite storage, finite Care</h3>\n\n\n\n<p>at my peak Korean study schedule i learned ~25 new words per day. i picked up 1000s of words in my first year, including stuff i&#8217;ve never needed to know like <em>ennui</em> (권태기).</p>\n\n\n\n<p>i didn&#8217;stop at 25 because my brain couldn&#8217;t handle more. i stopped because my brain didn&#8217;t care. try using 25 new words tonight between dinner and bed time.</p>\n\n\n\n<p>learning because We Can ignores our most important limiting function &#8212; our slow-to-refill supply of attention and the requisite behaviors (energy) necessary to assist our attention&#8217;s current target.</p>\n\n\n\n<h3 class=\"wp-block-heading\">another approach to Googling things</h3>\n\n\n\n<p>before looking up a dumb (or useful) piece of information, ask yourself if you <em>want</em> to care about that information tomorrow, the next day, and a year after.</p>\n\n\n\n<p>i&#8217;ll go first and admit i&#8217;ve heard the words &#8220;Docker&#8221; and &#8220;Kubernetes&#8221; 100s of times since learning to code. i&#8217;m curious what these words mean. but i care about my health, my family, and my future. so for now, i won&#8217;t be looking them up.</p>\n\n\n\n<p>intentional ignorance is wisdom. steward your Care.</p>\n<p>The post <a rel=\"nofollow\" href=\"https://www.ryanckulp.com/learner-beware-to-know-is-to-care/\">Learner Beware: to Know is to Care</a> appeared first on <a rel=\"nofollow\" href=\"https://www.ryanckulp.com\">Ryan Kulp</a>.</p>\n",
      # }],
      rss_feed: [
        {title: "Garfield by Jim Davis for Fri, 06 Jun 2025",
        content: "<p>\n\t<img src=\"https://featureassets.gocomics.com/assets/a0b898b017c5013ea0c4005056a9545d\" alt=\"Garfield by Jim Davis on Fri, 06 Jun 2025\" title=\"Garfield by Jim Davis on Fri, 06 Jun 2025\">\n</p>\n<p>\n\t<a href=\"https://www.gocomics.com/garfield/2025/06/06\">Source</a> -\n\t<a href=\"https://www.patreon.com/bePatron?u=6855838\">Patreon</a>\n</p>\n"},
        {title: "Garfield by Jim Davis for Thu, 05 Jun 2025",
         content: "<p>\n\t<img src=\"https://featureassets.gocomics.com/assets/baeb4ee017c8013ea0c5005056a9545d\" alt=\"Garfield by Jim Davis on Thu, 05 Jun 2025\" title=\"Garfield by Jim Davis on Thu, 05 Jun 2025\">\n</p>\n<p>\n\t<a href=\"https://www.gocomics.com/garfield/2025/06/05\">Source</a> -\n\t<a href=\"https://www.patreon.com/bePatron?u=6855838\">Patreon</a>\n</p>\n"},
        { title: "Garfield by Jim Davis for Wed, 04 Jun 2025",
          content: "<p>\n\t<img src=\"https://featureassets.gocomics.com/assets/9de1caf017c5013ea0c4005056a9545d\" alt=\"Garfield by Jim Davis on Wed, 04 Jun 2025\" title=\"Garfield by Jim Davis on Wed, 04 Jun 2025\">\n</p>\n<p>\n\t<a href=\"https://www.gocomics.com/garfield/2025/06/04\">Source</a> -\n\t<a href=\"https://www.patreon.com/bePatron?u=6855838\">Patreon</a>\n</p>\n"},
        ],
      instance_name: 'My RSS Feed',
    },
    email_meter: {
      instance_name: '<EMAIL>',
      metrics: {
        messages_sent: 220,
        recipients: 96,
        avg_response_time: { hours: 22, minutes: 51, seconds: 27 },
        messages_received: 1662,
        senders: 300
      }
    },
    close: {
      instance_name: 'GetReviews.ai',
      opportunities: {
        "Organic Signup": [
          {
            "name": "GetReviews",
            "type": "active",
            "value": "$19 monthly",
            "user": "Hideko",
            "status": "Organic Signup"
          },
          {
            "name": "Fomo.com",
            "type": "active",
            "value": "$79 monthly",
            "user": "Ryan",
            "status": "Organic Signup"
          },
          {
            "name": "Gimbap NFT",
            "type": "active",
            "value": "$420 yearly",
            "user": "via Joy",
            "status": "Organic Signup"
          },
          {
            "name": "ArtSpot",
            "type": "active",
            "value": "$99 monthly",
            "user": "Grace",
            "status": "Organic Signup"
          },
          {
            "name": "Fork Equity",
            "type": "active",
            "value": "$399 monthly",
            "user": "Rebecca",
            "status": "Organic Signup"
          }
        ],
        "Demo Completed": [
          {
            "name": "TechSavvy",
            "type": "active",
            "value": "$29 monthly",
            "user": "Alice",
            "status": "Organic Signup"
          },
          {
            "name": "HealthPlus",
            "type": "active",
            "value": "$59 monthly",
            "user": "Bob",
            "status": "Organic Signup"
          },
          {
            "name": "EduLearn",
            "type": "active",
            "value": "$99 yearly",
            "user": "Charlie",
            "status": "Organic Signup"
          },
          {
            "name": "FitLife",
            "type": "active",
            "value": "$49 monthly",
            "user": "David",
            "status": "Organic Signup"
          },
          {
            "name": "TravelMate",
            "type": "active",
            "value": "$79 yearly",
            "user": "Eve",
            "status": "Organic Signup"
          },
          {
            "name": "ShopSmart",
            "type": "active",
            "value": "$39 monthly",
            "user": "Frank",
            "status": "Organic Signup"
          },
          {
            "name": "GreenEnergy",
            "type": "active",
            "value": "$89 yearly",
            "user": "Grace",
            "status": "Organic Signup"
          },
          {
            "name": "PetCare",
            "type": "active",
            "value": "$19 monthly",
            "user": "Hank",
            "status": "Organic Signup"
          },
          {
            "name": "AutoFix",
            "type": "active",
            "value": "$49 monthly",
            "user": "Ivy",
            "status": "Organic Signup"
          },
          {
            "name": "HomeSecure",
            "type": "active",
            "value": "$99 yearly",
            "user": "Jack",
            "status": "Organic Signup"
          }
        ],
        "Onboarded": [
          {
            "name": "Foodie",
            "type": "active",
            "value": "$29 monthly",
            "user": "Karen",
            "status": "Organic Signup"
          },
          {
            "name": "GadgetPro",
            "type": "active",
            "value": "$59 monthly",
            "user": "Leo",
            "status": "Organic Signup"
          }
        ]
      },
      name: 'Sales'
    },
    hubspot: {
      instance_name: 'GetReviews.ai',
      opportunities: {
        "Organic Signup": [
          {
            "name": "GetReviews",
            "type": "active",
            "value": "$19 monthly",
            "user": "Hideko",
            "status": "Organic Signup"
          },
          {
            "name": "Fomo.com",
            "type": "active",
            "value": "$79 monthly",
            "user": "Ryan",
            "status": "Organic Signup"
          },
          {
            "name": "Gimbap NFT",
            "type": "active",
            "value": "$420 yearly",
            "user": "via Joy",
            "status": "Organic Signup"
          },
          {
            "name": "ArtSpot",
            "type": "active",
            "value": "$99 monthly",
            "user": "Grace",
            "status": "Organic Signup"
          },
          {
            "name": "Fork Equity",
            "type": "active",
            "value": "$399 monthly",
            "user": "Rebecca",
            "status": "Organic Signup"
          },
          {
            "name": "TechSavvy",
            "type": "active",
            "value": "$29 monthly",
            "user": "Alice",
            "status": "Organic Signup"
          },
          {
            "name": "HealthPlus",
            "type": "active",
            "value": "$59 monthly",
            "user": "Bob",
            "status": "Organic Signup"
          },
          {
            "name": "EduLearn",
            "type": "active",
            "value": "$99 yearly",
            "user": "Charlie",
            "status": "Organic Signup"
          },
          {
            "name": "FitLife",
            "type": "active",
            "value": "$49 monthly",
            "user": "David",
            "status": "Organic Signup"
          }
        ],
        "Demo Completed": [
          {
            "name": "Cross Sell",
            "type": "active",
            "value": '$249',
            "user": "Steve",
            "status": "Demo Completed"
          },
          {
            "name": "Picture It",
            "type": "active",
            "value": '$500',
            "user": "Alex",
            "status": "Demo Completed"
          },
          {
            "name": "MarketHub",
            "type": "active",
            "value": '$300',
            "user": "Mia",
            "status": "Demo Completed"
          },
          {
            "name": "SalesBoost",
            "type": "active",
            "value": '$450',
            "user": "Noah",
            "status": "Demo Completed"
          },
          {
            "name": "TechWave",
            "type": "active",
            "value": '$600',
            "user": "Olivia",
            "status": "Demo Completed"
          },
        ],
        "Onboarded": [
          {
            "name": "HealthFirst",
            "type": "active",
            "value": "$59 monthly",
            "user": "Ben",
            "status": "Onboarded"
          },
          {
            "name": "EduWorld",
            "type": "active",
            "value": "$99 yearly",
            "user": "Cathy",
            "status": "Onboarded"
          }
        ]
      },
      name: 'Sales'
    },
    square_pos: {
      instance_name: 'Beardbrand HQ',
      "payments": {
        "Main Street Café": [
          { "amount": 6.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CASH" },
          { "amount": 32.85, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CASH" },
          { "amount": 4.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CASH" },
          { "amount": 6.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CASH" },
          { "amount": 47.60, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 3.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 5.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 19.35, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 3.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 19.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 5.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 45.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 40.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 89.40, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 22.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 4.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 13.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 10.25, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.45, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 13.90, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 27.80, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 20.85, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 34.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 7.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 11.90, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 23.80, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 32.85, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 4.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 47.60, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 3.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 5.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 19.35, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 3.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 19.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 5.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 45.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 40.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 89.40, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 22.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 4.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 13.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 10.25, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.45, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 13.90, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 27.80, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" }
        ],
        "Maple Road Stand": [
          { "amount": 3.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 11.25, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 45.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 5.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CASH" },
          { "amount": 7.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 18.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 34.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 28.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 24.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 18.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 9.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 15.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CASH" },
          { "amount": 3.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 7.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 13.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 11.90, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 3.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 5.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.45, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 16.35, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 11.90, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 23.80, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 18.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 9.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.45, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 5.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 18.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 5.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 13.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 3.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 11.25, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 45.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 5.95, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 7.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 18.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 34.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 28.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 24.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 18.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 9.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 15.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" }
        ],
        "City Mall Kiosk": [
          { "amount": 34.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 10.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CASH" },
          { "amount": 3.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 20.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 11.90, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.45, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 12.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 13.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 9.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 3.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 18.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 8.75, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 19.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 14.25, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CASH" },
          { "amount": 27.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 7.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 12.90, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.45, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 13.90, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 8.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 25.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 15.25, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 6.45, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 7.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 16.25, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 9.50, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 27.80, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 12.90, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" },
          { "amount": 27.00, "currency": "USD", "location": "Harvest Restaurant", "source_type": "CARD" }
        ]
      }
    },
    weather: {
      # BEGIN WeatherAPI demo data
      # temperature: 76.6,
      # data_provider: 'weatherapi',
      # forecast: {
      #   today: { mintemp: 69.5, maxtemp: 86.2, day_override: Date.today.strftime("%b %-0d"), conditions: "Partly cloudy", uv_index: "High (8)" },
      #   tomorrow: { mintemp: 65.2, maxtemp: 79.1, day_override: (Date.today + 1).strftime("%b %-0d"), conditions: "Light Rain", uv_index: "Moderate (5)" }
      # },
      # weather_image: "wi-day-sunny",
      # today_weather_image: "wi-day-cloudy",
      # tomorrow_weather_image: "wi-day-showers",
      # conditions: "Sunny",
      # debug: {},
      # instance_name: 'Las Vegas',
      # humidity: 45,
      # feels_like: 79.7,
      # last_updated: DateTime.now.strftime("%l:%M %p"),
      # errors: "No matching location found." # enable to test error views
      # END WeatherAPI demo data

      # BEGIN Tempest demo data
      data_provider: "tempest",
      temperature: 79,
      forecast: {
        right_now: {
          feels_like: 79,
          humidity: 58,
          icon: "partly-cloudy-day",
          temperature: 79,
          sunrise: "06:05",
          sunset: "20:05",
          sunrise_unix: **********,
          sunset_unix: **********,
          wind: {
            direction_cardinal: "SW", gust: 4.0, units: "kph"
          }
        },
        today: {
          icon: "possibly-thunderstorm-day",
          mintemp: 59,
          maxtemp: 79,
          day_override: nil,
          conditions: "Thunderstorms Possible",
          uv_index: 5,
          precip: {
            icon: "chance-rain", probability: 20, amount: nil, units: "in"
          }
        },
        tomorrow: {
          icon: "partly-cloudy-day",
          mintemp: 63,
          maxtemp: 84,
          day_override: nil,
          conditions: "Partly Cloudy",
          uv_index: 0,
          precip: {
            icon: "chance-rain", probability: 0
          }
        }
      },
      weather_image: "#{Rails.application.credentials.base_url}/images/plugins/weather/wi-day-sunny.svg",
      today_weather_image: "https://tempestwx.com/images/Updated/possibly-thunderstorm-day.svg",
      tomorrow_weather_image: "https://tempestwx.com/images/Updated/partly-cloudy-day.svg",
      conditions: "Thunderstorms Possible",
      humidity: 58,
      feels_like: 79,
      instance_name: 'Gold Coast'
      # END Tempest demo data
    },
    mlb_scores: {
      instance_name: 'Braves',
      boxscore: {
        venue: "Lee Health Sports Complex",
        home_team: {
          "name" => "Minnesota Twins",
          "record" =>
            { "gamesPlayed" => 27,
              "wildCardGamesBack" => "-",
              "leagueGamesBack" => "-",
              "springLeagueGamesBack" => "-",
              "sportGamesBack" => "-",
              "divisionGamesBack" => "-",
              "conferenceGamesBack" => "-",
              "leagueRecord" => { "wins" => 8, "losses" => 19, "ties" => 0, "pct" => ".296" },
              "records" => {},
              "divisionLeader" => false,
              "wins" => 8,
              "losses" => 19,
              "winningPercentage" => ".296" },
          "stats" =>
            { "batting" =>
                { "flyOuts" => 0,
                  "groundOuts" => 0,
                  "runs" => 3,
                  "doubles" => 0,
                  "triples" => 0,
                  "homeRuns" => 0,
                  "strikeOuts" => 0,
                  "baseOnBalls" => 0,
                  "intentionalWalks" => 0,
                  "hits" => 0,
                  "hitByPitch" => 0,
                  "avg" => ".237",
                  "atBats" => 0,
                  "obp" => ".306",
                  "slg" => ".365",
                  "ops" => ".671",
                  "caughtStealing" => 0,
                  "stolenBases" => 0,
                  "stolenBasePercentage" => ".---",
                  "groundIntoDoublePlay" => 0,
                  "groundIntoTriplePlay" => 0,
                  "plateAppearances" => 0,
                  "totalBases" => 0,
                  "rbi" => 0,
                  "leftOnBase" => 0,
                  "sacBunts" => 0,
                  "sacFlies" => 0,
                  "catchersInterference" => 0,
                  "pickoffs" => 0,
                  "atBatsPerHomeRun" => "-.--" },
              "pitching" =>
                { "groundOuts" => 0,
                  "airOuts" => 0,
                  "runs" => 5,
                  "doubles" => 0,
                  "triples" => 0,
                  "homeRuns" => 2,
                  "strikeOuts" => 0,
                  "baseOnBalls" => 0,
                  "intentionalWalks" => 0,
                  "hits" => 0,
                  "hitByPitch" => 0,
                  "atBats" => 0,
                  "obp" => ".000",
                  "caughtStealing" => 0,
                  "stolenBases" => 0,
                  "stolenBasePercentage" => ".---",
                  "numberOfPitches" => 0,
                  "era" => "4.98",
                  "inningsPitched" => "0.0",
                  "saveOpportunities" => 0,
                  "earnedRuns" => 0,
                  "whip" => "1.39",
                  "battersFaced" => 0,
                  "outs" => 0,
                  "completeGames" => 0,
                  "shutouts" => 0,
                  "balls" => 0,
                  "strikes" => 0,
                  "strikePercentage" => "-.--",
                  "hitBatsmen" => 0,
                  "balks" => 0,
                  "wildPitches" => 0,
                  "pickoffs" => 0,
                  "groundOutsToAirouts" => "-.--",
                  "rbi" => 0,
                  "pitchesPerInning" => "-.--",
                  "runsScoredPer9" => "-.--",
                  "homeRunsPer9" => "-.--",
                  "inheritedRunners" => 0,
                  "inheritedRunnersScored" => 0,
                  "catchersInterference" => 0,
                  "sacBunts" => 0,
                  "sacFlies" => 0,
                  "passedBall" => 0 },
              "fielding" =>
                { "caughtStealing" => 0,
                  "stolenBases" => 0,
                  "stolenBasePercentage" => ".---",
                  "assists" => 0,
                  "putOuts" => 0,
                  "errors" => 0,
                  "chances" => 0,
                  "passedBall" => 0,
                  "pickoffs" => 0 } } },
        away_team:
          { "name" => "Atlanta Braves",
            "record" =>
              { "gamesPlayed" => 26,
                "wildCardGamesBack" => "-",
                "leagueGamesBack" => "-",
                "springLeagueGamesBack" => "-",
                "sportGamesBack" => "-",
                "divisionGamesBack" => "-",
                "conferenceGamesBack" => "-",
                "leagueRecord" => { "wins" => 12, "losses" => 14, "ties" => 0, "pct" => ".462" },
                "records" => {},
                "divisionLeader" => false,
                "wins" => 12,
                "losses" => 14,
                "winningPercentage" => ".462" },
            "stats" =>
              { "batting" =>
                  { "flyOuts" => 0,
                    "groundOuts" => 0,
                    "runs" => 5,
                    "doubles" => 0,
                    "triples" => 0,
                    "homeRuns" => 1,
                    "strikeOuts" => 0,
                    "baseOnBalls" => 0,
                    "intentionalWalks" => 0,
                    "hits" => 0,
                    "hitByPitch" => 0,
                    "avg" => ".235",
                    "atBats" => 0,
                    "obp" => ".312",
                    "slg" => ".374",
                    "ops" => ".686",
                    "caughtStealing" => 0,
                    "stolenBases" => 0,
                    "stolenBasePercentage" => ".---",
                    "groundIntoDoublePlay" => 0,
                    "groundIntoTriplePlay" => 0,
                    "plateAppearances" => 0,
                    "totalBases" => 0,
                    "rbi" => 0,
                    "leftOnBase" => 0,
                    "sacBunts" => 0,
                    "sacFlies" => 0,
                    "catchersInterference" => 0,
                    "pickoffs" => 0,
                    "atBatsPerHomeRun" => "-.--" },
                "pitching" =>
                  { "groundOuts" => 0,
                    "airOuts" => 0,
                    "runs" => 3,
                    "doubles" => 0,
                    "triples" => 0,
                    "homeRuns" => 0,
                    "strikeOuts" => 0,
                    "baseOnBalls" => 0,
                    "intentionalWalks" => 0,
                    "hits" => 0,
                    "hitByPitch" => 0,
                    "atBats" => 0,
                    "obp" => ".000",
                    "caughtStealing" => 0,
                    "stolenBases" => 0,
                    "stolenBasePercentage" => ".---",
                    "numberOfPitches" => 0,
                    "era" => "4.31",
                    "inningsPitched" => "0.0",
                    "saveOpportunities" => 0,
                    "earnedRuns" => 0,
                    "whip" => "1.42",
                    "battersFaced" => 0,
                    "outs" => 0,
                    "completeGames" => 0,
                    "shutouts" => 0,
                    "balls" => 0,
                    "strikes" => 0,
                    "strikePercentage" => "-.--",
                    "hitBatsmen" => 0,
                    "balks" => 0,
                    "wildPitches" => 0,
                    "pickoffs" => 0,
                    "groundOutsToAirouts" => "-.--",
                    "rbi" => 0,
                    "pitchesPerInning" => "-.--",
                    "runsScoredPer9" => "-.--",
                    "homeRunsPer9" => "-.--",
                    "inheritedRunners" => 0,
                    "inheritedRunnersScored" => 0,
                    "catchersInterference" => 0,
                    "sacBunts" => 0,
                    "sacFlies" => 0,
                    "passedBall" => 0 },
                "fielding" =>
                  { "caughtStealing" => 0,
                    "stolenBases" => 0,
                    "stolenBasePercentage" => ".---",
                    "assists" => 0,
                    "putOuts" => 0,
                    "errors" => 0,
                    "chances" => 0,
                    "passedBall" => 0,
                    "pickoffs" => 0
                  }
              }
          }
      }
    },
    language_learning: {
      instance_name: 'Japanese',
      language: 'Japanese',
      word: 'terminal',
      translation: 'ターミナル',
      usage: 'シンガポールへのフライトは第一ターミナルから出発します'
    },
    image_display: {
      image_url: 'https://i.imgur.com/75bR25p.jpeg'
      # image_url: 'https://snell.zone/weather/bw-trmnl.bmp'
    },
    motivational_quote: {
      quote: {
        content: "The World is my country, all mankind are my brethren, and to do good is my religion.",
        author: "Thomas Paine"
      }
    },
    postmark_analytics: {
      metrics: {
        sent: 4827,
        bounced: 23,
        errors: 89,
        bounce_rate: 0.48,
        spam_complaints: 2,
        open: 3279,
        clicks: 658,
        open_rate: 67.9
      },
      instance_name: 'GetReviews'
    },
    gumroad_analytics: {
      metrics: {
        sales_count: 8264,
        sales_volume: 49246.00
      },
      instance_name: 'Programming E-Book'
    },
    lunch_money: {
      instance_name: 'Budgets', # Accounts, Budgets
      # items: [
      #   ["Pottery Wheel", -750],
      #   ["Crypto", 900],
      #   ["Cash under pillow", 475],
      #   ["Gift cards", 169]
      # ],
      items: [
        ["Mortgage / Rent", 850],
        ["Shopping", 401.**************],
        ["Self-improvement", 384.**************],
        ["Travel", 300.91],
        ["Utilities", 265],
        ["Groceries", 192.**************],
        ["Personal Care", 180],
        ["Gifts", 147.*************],
        ["Restaurants", 101.*************],
        ["Alcohol, Bars", 85.88],
        ["Home furnishings", 85.51],
        ["Coffee Shops", 34.7],
        ["Entertainment", 20.***************],
        ["Food Delivery", 304],
        ["Insurance", 25.99]
      ]
    },
    days_left_until: {
      instance_name: 'Kids Move Out',
      days_passed: 113,
      days_left: 23,
      percent_passed: 83,
      show_days_passed: true,
      show_days_left: true,
      message: 'Live a little!'
    },
    days_since: {
      instance_name: 'I Quit Smoking',
      start_date_formatted: (Date.today - 42.days).strftime("%b %d, %Y"),
      days_passed: 42,
    },
    private_plugin: {
      markup: '<div class="view view--full">
                <div class="layout">
                  <div class="columns">
                    <div class="column">
                      <div class="markdown gap--large">
                        <span class="title">Example Private Plugin</span>
                        <div class="content-element content content--center" data-content-max-height="286">{{ text }}</div>
                        <span class="label label--underline mt-4">- {{ author }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="title_bar">
                  <img class="image" src="https://usetrmnl.com/images/plugins/trmnl--render.svg" />
                  <span class="title">Title</span>
                  <span class="instance">Instance</span>
                </div>
              </div>',
      merge_variables: { 'text' => 'all you have to do is whatever it takes', 'author' => 'Kulp' }
    },
    screenshot: {
      image_url: 'https://image.thum.io/get/width/800/crop/721/https://google.com'
    },
    simple_analytics: {
      instance_name: 'usetrmnl.com',
      histogram:
        [{ date: "2024-06-09", "pageviews" => 975, "visitors" => 0 },
         { date: "2024-06-10", "pageviews" => 840, "visitors" => 0 },
         { date: "2024-06-11", "pageviews" => 1004, "visitors" => 0 },
         { date: "2024-06-12", "pageviews" => 1308, "visitors" => 0 },
         { date: "2024-06-13", "pageviews" => 753, "visitors" => 0 },
         { date: "2024-06-14", "pageviews" => 600, "visitors" => 0 },
         { date: "2024-06-15", "pageviews" => 710, "visitors" => 0 },
         { date: "2024-06-16", "pageviews" => 489, "visitors" => 0 },
         { date: "2024-06-17", "pageviews" => 510, "visitors" => 0 },
         { date: "2024-06-18", "pageviews" => 590, "visitors" => 0 },
         { date: "2024-06-19", "pageviews" => 610, "visitors" => 0 },
         { date: "2024-06-20", "pageviews" => 671, "visitors" => 0 },
         { date: "2024-06-21", "pageviews" => 512, "visitors" => 0 },
         { date: "2024-06-22", "pageviews" => 550, "visitors" => 0 },
         { date: "2024-06-23", "pageviews" => 421, "visitors" => 0 },
         { date: "2024-06-24", "pageviews" => 315, "visitors" => 0 },
         { date: "2024-06-25", "pageviews" => 604, "visitors" => 0 },
         { date: "2024-06-26", "pageviews" => 672, "visitors" => 0 },
         { date: "2024-06-27", "pageviews" => 601, "visitors" => 0 },
         { date: "2024-06-28", "pageviews" => 705, "visitors" => 0 },
         { date: "2024-06-29", "pageviews" => 800, "visitors" => 0 },
         { date: "2024-06-30", "pageviews" => 912, "visitors" => 0 },
         { date: "2024-07-01", "pageviews" => 1503, "visitors" => 0 },
         { date: "2024-07-02", "pageviews" => 1273, "visitors" => 0 },
         { date: "2024-07-03", "pageviews" => 1250, "visitors" => 0 },
         { date: "2024-07-04", "pageviews" => 1198, "visitors" => 0 },
         { date: "2024-07-05", "pageviews" => 1005, "visitors" => 0 },
         { date: "2024-07-06", "pageviews" => 1300, "visitors" => 0 },
         { date: "2024-07-07", "pageviews" => 1103, "visitors" => 0 },
         { date: "2024-07-08", "pageviews" => 1004, "visitors" => 4 },
         { date: "2024-07-09", "pageviews" => 600, "visitors" => 0 }],
      metrics: {
        pageviews: 25388, visitors: 4771, mins_on_page: 2.23
      }
    },
    clicky: {
      instance_name: 'usetrmnl.com',
      histogram: [
        { date: "2024-06-09", pageviews: 975 },
        { date: "2024-06-10", pageviews: 840 },
        { date: "2024-06-11", pageviews: 1004 },
        { date: "2024-06-12", pageviews: 1308 },
        { date: "2024-06-13", pageviews: 753 },
        { date: "2024-06-14", pageviews: 600 },
        { date: "2024-06-15", pageviews: 710 },
        { date: "2024-06-16", pageviews: 489 },
        { date: "2024-06-17", pageviews: 510 },
        { date: "2024-06-18", pageviews: 590 },
        { date: "2024-06-19", pageviews: 610 },
        { date: "2024-06-20", pageviews: 671 },
        { date: "2024-06-21", pageviews: 512 },
        { date: "2024-06-22", pageviews: 550 },
        { date: "2024-06-23", pageviews: 421 },
        { date: "2024-06-24", pageviews: 315 },
        { date: "2024-06-25", pageviews: 604 },
        { date: "2024-06-26", pageviews: 672 },
        { date: "2024-06-27", pageviews: 601 },
        { date: "2024-06-28", pageviews: 705 },
        { date: "2024-06-29", pageviews: 800 },
        { date: "2024-06-30", pageviews: 912 },
        { date: "2024-07-01", pageviews: 1503 },
        { date: "2024-07-02", pageviews: 1273 },
        { date: "2024-07-03", pageviews: 1250 },
        { date: "2024-07-04", pageviews: 1198 },
        { date: "2024-07-05", pageviews: 1005 },
        { date: "2024-07-06", pageviews: 1300 },
        { date: "2024-07-07", pageviews: 1103 },
        { date: "2024-07-08", pageviews: 1004 },
        { date: "2024-07-09", pageviews: 600 }
      ],
      metrics: {
        pageviews: 25388, visitors: 4771, mins_on_page: 2.23
      }
    },
    google_analytics: {
      instance_name: 'usetrmnl.com',
      histogram: [
        { date: "2024-06-09", pageviews: 975 },
        { date: "2024-06-10", pageviews: 840 },
        { date: "2024-06-11", pageviews: 1004 },
        { date: "2024-06-12", pageviews: 1308 },
        { date: "2024-06-13", pageviews: 753 },
        { date: "2024-06-14", pageviews: 600 },
        { date: "2024-06-15", pageviews: 710 },
        { date: "2024-06-16", pageviews: 489 },
        { date: "2024-06-17", pageviews: 510 },
        { date: "2024-06-18", pageviews: 590 },
        { date: "2024-06-19", pageviews: 610 },
        { date: "2024-06-20", pageviews: 671 },
        { date: "2024-06-21", pageviews: 512 },
        { date: "2024-06-22", pageviews: 550 },
        { date: "2024-06-23", pageviews: 421 },
        { date: "2024-06-24", pageviews: 315 },
        { date: "2024-06-25", pageviews: 604 },
        { date: "2024-06-26", pageviews: 672 },
        { date: "2024-06-27", pageviews: 601 },
        { date: "2024-06-28", pageviews: 705 },
        { date: "2024-06-29", pageviews: 800 },
        { date: "2024-06-30", pageviews: 912 },
        { date: "2024-07-01", pageviews: 1503 },
        { date: "2024-07-02", pageviews: 1273 },
        { date: "2024-07-03", pageviews: 1250 },
        { date: "2024-07-04", pageviews: 1198 },
        { date: "2024-07-05", pageviews: 1005 },
        { date: "2024-07-06", pageviews: 1300 },
        { date: "2024-07-07", pageviews: 1103 },
        { date: "2024-07-08", pageviews: 1004 },
        { date: "2024-07-09", pageviews: 600 }
      ],
      metrics: {
        pageviews: "2535629388", sessions: 1535629388, visitors: 535629388, mins_on_page: "2.23"
      }
    },
    chatgpt: {
      instance_name: 'Science for curious minds',
      answer: "Did you know that the study of Fibonacci numbers in plants reveals fascinating patterns in nature? The Fibonacci sequence, where each number is the sum of the two preceding ones (0, 1, 1, 2, 3, 5, 8, 13, and so on), can be seen in the arrangement of leaves, petals, and seeds in plants.\n\nIn sunflowers, for example, the seeds are arranged in spirals that follow Fibonacci numbers, allowing for efficient packing and maximizing the number of seeds. Similarly, pinecones and pineapples exhibit Fibonacci spirals in their structure.\n\nThis natural phenomenon, known as phyllotaxis, showcases the inherent mathematical beauty found in plants and demonstrates how they optimize their growth and reproductive processes. The presence of Fibonacci numbers in nature continues to inspire artists, architects, and scientists alike, highlighting the interconnectedness of mathematics and the natural world.\n\nNext time you observe a sunflower or a pinecone, take a moment to appreciate the hidden mathematical marvels that contribute to their beauty and efficiency."
    },
    readwise: {
      highlights: {
        :author => "David  Ogilvy",
        :text => "Few of the great creators have bland personalities. ",
        :title => "Confessions of an Advertising Man"
      }
    },
    the_office: {
      quote: {
        character: "Dwight Schrute",
        content: "I hope the war goes on forever and Ryan gets drafted. I’m sorry, only part of me meant that. He’d probably end up a hero there, anyway."
      }
    },
    polymarket: {
      instance_name: 'Crypto Stuff',
      events: [
        {
          question: "Ethereum ETF begins trading by July 26?",
          volume: 463327.3,
          comments: 33,
          icon: "https://polymarket-upload.s3.us-east-2.amazonaws.com/ethereum-etf-begins-trading-by-july-26-34qGXC0FPfy8.jpg",
          outcomes: [{ "Yes": "0.9875" }, { "No": "0.0125" }] },
        {
          question: "Trump says \"mog\" at Bitcoin Conference?",
          volume: 55617.03,
          comments: 9,
          icon: "https://polymarket-upload.s3.us-east-2.amazonaws.com/trump+mog.jpeg",
          outcomes: [{ "Yes": "0.055" }, { "No": "0.945" }] },
        {
          question: "Bitcoin above $65,000 on July 26?",
          volume: 31165.79,
          comments: 7,
          icon: "https://polymarket-upload.s3.us-east-2.amazonaws.com/bitcoin+neon+red+green.png",
          outcomes: [{ "Yes": "0.8" }, { "No": "0.2" }] },
        {
          question: "TikTok sale announced before August?",
          volume: 57251.92,
          comments: 1,
          icon: "https://polymarket-upload.s3.us-east-2.amazonaws.com/tiktok-sale-announced-before-august-bddff11a-23d4-4ae2-a4e0-637aa9f94504.png",
          outcomes: [{ "Yes" => "0.0195" }, { "No" => "0.9805" }] },
        {
          question: "Ethereum above $3,500 on July 26?",
          volume: 37203.62,
          comments: 0,
          icon: "https://polymarket-upload.s3.us-east-2.amazonaws.com/ethereum+logo+confetti.png",
          outcomes: [{ "Yes": "0.6" }, { "No": "0.4" }] },
        {
          question: "Will Elon Musk give a speech at Bitcoin conference? ",
          volume: 2028.53,
          comments: 1,
          icon: "https://polymarket-upload.s3.us-east-2.amazonaws.com/will-elon-speak-at-hRhDhprouRGA.jpg",
          outcomes: [{ "Yes" => "0.365" }, { "No" => "0.635" }] }
      ]
    },
    clock: { datetime: DateTime.now.to_i, time_zone: 'Asia/Calcutta' },
    custom_text: {
      instance_name: 'Big Ideas',
      phrase: "Every meal you eat is one less meal you'll eat.",
      font_scaling: 'automatic', # automatic, fixed
      title_bar_text: 'Chinese Proverbs'
    },
    beehiiv: {
      metrics: {
        subject: 'Check this out',
        click_rate: 10,
        clicks: 10,
        open: 80,
        open_rate: 0,
        sent: 100,
        spam_complaints: 1,
        unsubscribes: 1
      },
      instance_name: 'My Newsletter',
    },
    mondrian: {},
    todo_list: {
      todos: [
        {
          label: 'doing',
          items: [
            'Review team progress',
            'Approve budget requests',
            'Prepare for team meeting by creating a detailed agenda and gathering necessary documents',
            'Coordinate with HR on new hires',
            'Provide feedback on project plans',
            'Review department expenses',
            'Monitor project deadlines',
          # 'Draft new company policies on remote work flexibility',
          # 'Plan training sessions for new hires',
          # 'Evaluate team workload distribution and reassign tasks where necessary',
          # 'Develop new project initiatives to drive growth in underperforming segments'
          ]
        },
        { label: 'done',
          items: [
            'Send monthly report to upper management',
            'Conduct one-on-one meetings with team members',
            'Finalize quarterly targets',
            'Complete performance reviews for all team members, highlighting areas of improvement',
            'Set up project timelines',
            'Implement new team structures',
            'Organize team-building activities',
            'Prepare performance improvement plans for underperforming employees with detailed steps',
            'Host departmental strategy session',
            'Update project documentation to reflect scope changes',
            'Review and finalize vendor contracts, ensuring terms align with procurement policies',
            'Prepare department budget for the next fiscal year',
            'Review team’s performance over the last quarter and prepare a report',
            'Conduct stakeholder interviews for the upcoming project initiative',
            'Finalize employee promotion decisions based on performance and feedback',
            'Create a new project roadmap for the next product cycle',
            'Submit final version of marketing plan for new product launch',
            'Collaborate with legal to finalize employee contracts for the new hires',
            'Complete risk analysis for upcoming projects and develop mitigation strategies'
          ]
        },
      ],
      instance_name: 'Work Tasks'
    },
    shopping_list: {
      items: [
        'Milk', 'cereal', 'more mature child', 'another', 'one', 'here', 'is', 'a', 'bigger', 'list', 'napkins', 'clothes hanger', 'turkey bacon'
      ],
      instance_name: 'Groceries'
    },
    upcoming_movies: {
      movie: {
        title: "Terrifier 3",
        overview: "Five years after surviving Art the Clown's Halloween massacre,
                   Sienna and her brother are still struggling to rebuild their shattered lives.
                   As the holiday season approaches, they try to embrace the Christmas spirit and
                   leave the horrors of the past behind. But just when they think they're safe,
                   Art returns, determined to turn their holiday cheer into a new nightmare.
                   The festive season quickly unravels as Art unleashes his twisted brand of terror,
                   proving that no holiday is safe.",
        release_date: "2024-10-09",
        poster_url: "https://image.tmdb.org/t/p/w440_and_h660_face/7NDHoebflLwL1CcgLJ9wZbbDrmV.jpg"
      },
      filter_by_label: 'Now Playing',
      instance_name: 'Showtimes'
    },
    salesforce: {
      opportunities: { "Won" =>
                         [{ :name => "Burlington Textiles Weaving Plant Generator",
                            :value => "235000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "Won",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Edge Emergency Generator", :value => "75000.0", :user => "RKulp", :status => "Closed Won", :type => "Won", :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Edge Installation", :value => "50000.0", :user => "RKulp", :status => "Closed Won", :type => "Won", :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Edge SLA", :value => "60000.0", :user => "RKulp", :status => "Closed Won", :type => "Won", :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Express Logistics Standby Generator",
                            :value => "220000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "Won",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "GenePoint SLA", :value => "30000.0", :user => "RKulp", :status => "Closed Won", :type => "Won", :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "GenePoint Standby Generator", :value => "85000.0", :user => "RKulp", :status => "Closed Won", :type => "Won", :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Grand Hotels Emergency Generators",
                            :value => "210000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "Won",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Grand Hotels Generator Installations",
                            :value => "350000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "Won",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Grand Hotels SLA", :value => "90000.0", :user => "RKulp", :status => "Closed Won", :type => "Won", :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Emergency Generators",
                            :value => "440000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "Won",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Installations", :value => "270000.0", :user => "RKulp", :status => "Closed Won", :type => "Won", :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Installations", :value => "235000.0", :user => "RKulp", :status => "Closed Won", :type => "Won", :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Refinery Generators",
                            :value => "915000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "Won",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil SLA", :value => "120000.0", :user => "RKulp", :status => "Closed Won", :type => "Won", :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Standby Generators",
                            :value => "120000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "Won",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "University of AZ Portable Generators",
                            :value => "50000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "Won",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "University of AZ SLA", :value => "90000.0", :user => "RKulp", :status => "Closed Won", :type => "Won", :updated => "Fri Oct 11 22:27:17 GMT 2024" }],
                       "My Opportunities" =>
                         [{ :name => "Burlington Textiles Weaving Plant Generator",
                            :value => "235000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Dickenson Mobile Generators",
                            :value => "15000.0",
                            :user => "RKulp",
                            :status => "Qualification",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Edge Emergency Generator",
                            :value => "75000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Edge Emergency Generator",
                            :value => "35000.0",
                            :user => "RKulp",
                            :status => "Id. Decision Makers",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Edge Installation",
                            :value => "50000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Edge SLA", :value => "60000.0", :user => "RKulp", :status => "Closed Won", :type => "My Opportunities", :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Express Logistics Portable Truck Generators",
                            :value => "80000.0",
                            :user => "RKulp",
                            :status => "Value Proposition",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Express Logistics SLA",
                            :value => "120000.0",
                            :user => "RKulp",
                            :status => "Perception Analysis",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Express Logistics Standby Generator",
                            :value => "220000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "GenePoint Lab Generators",
                            :value => "60000.0",
                            :user => "RKulp",
                            :status => "Id. Decision Makers",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "GenePoint SLA", :value => "30000.0", :user => "RKulp", :status => "Closed Won", :type => "My Opportunities", :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "GenePoint Standby Generator",
                            :value => "85000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Grand Hotels Emergency Generators",
                            :value => "210000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Grand Hotels Generator Installations",
                            :value => "350000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Grand Hotels Guest Portable Generators",
                            :value => "250000.0",
                            :user => "RKulp",
                            :status => "Value Proposition",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Grand Hotels Kitchen Generator",
                            :value => "15000.0",
                            :user => "RKulp",
                            :status => "Id. Decision Makers",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Grand Hotels SLA",
                            :value => "90000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "Pyramid Emergency Generators",
                            :value => "100000.0",
                            :user => "RKulp",
                            :status => "Prospecting",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Emergency Generators",
                            :value => "440000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Installations",
                            :value => "270000.0",
                            :user => "RKulp",
                            :status => "Negotiation/Review",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Installations",
                            :value => "270000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Installations",
                            :value => "235000.0",
                            :user => "RKulp",
                            :status => "Closed Won",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Office Portable Generators",
                            :value => "125000.0",
                            :user => "RKulp",
                            :status => "Negotiation/Review",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Plant Standby Generators",
                            :value => "675000.0",
                            :user => "RKulp",
                            :status => "Needs Analysis",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" },
                          { :name => "United Oil Refinery Generators",
                            :value => "270000.0",
                            :user => "RKulp",
                            :status => "Proposal/Price Quote",
                            :type => "My Opportunities",
                            :updated => "Fri Oct 11 22:27:17 GMT 2024" }] },
      instance_name: 'Pipeline'
    },
    statuspage: {
      pages: {
        "incidents" =>
          [
            {
              title: "Degraded Performance for reddit.com",
              date: 2.hours.ago.to_datetime,
              content: "\n<p><small>Sep <var data-var='date'>19</var>, <var data-var='time'>13:22</var> PDT</small><br><strong>Resolved</strong> - This incident has been resolved.</p><p><small>Sep <var data-var='date'>19</var>, <var data-var='time'>13:09</var> PDT</small><br><strong>Monitoring</strong> - We have identified the underlying cause of the issues and have rolled out a fix.</p><p><small>Sep <var data-var='date'>19</var>, <var data-var='time'>12:15</var> PDT</small><br><strong>Investigating</strong> - We are currently investigating this issue.</p>      ",
              site_name: "Reddit",
              icon: "shield_warning" },
            {
              title: "Upstream messaging provider Issue for some accounts",
              date: 3.days.ago.to_datetime,
              content: "\n<p><small>Aug <var data-var='date'>28</var>, <var data-var='time'>15:03</var> PDT</small><br><strong>Resolved</strong> - Our upstream provider indicates that normal operational status has returned.  We will be crediting customers with affected broadcasts.  If you have an affected broadcast, please attempt to retarget the messages that failed with the 35125 error code.</p><p><small>Aug <var data-var='date'>28</var>, <var data-var='time'>13:32</var> PDT</small><br><strong>Investigating</strong> - There is a problem some customers are experiencing due to an issue with an upstream messaging partner.  This is resulting in improper error code (35125) being returned during some broadcasts.  We are working with the upstream provider to get this resolved as soon as possible.  Suggest pausing large outbound broadcasts while this is being resolved.</p>      ",
              site_name: "Prompt.io",
              icon: "hexagon"
            },
            {
              title: "Freeze and unfreeze services down",
              date: 21.days.ago.to_datetime,
              content: "\n<p><small>Jul <var data-var='date'>12</var>, <var data-var='time'>03:24</var> UTC</small><br><strong>Resolved</strong> - This incident has been resolved.</p><p><small>Jul <var data-var='date'>12</var>, <var data-var='time'>03:16</var> UTC</small><br><strong>Monitoring</strong> - A fix has been implemented and we are monitoring the results.</p><p><small>Jul <var data-var='date'>12</var>, <var data-var='time'>02:00</var> UTC</small><br><strong>Investigating</strong> - Container for background jobs is failing</p>      ",
              site_name: "Merge Freeze",
              icon: "hexagon"
            }
          ],
        "scheduled" =>
          [
            {
              title: "Scheduled maintenance for Swish - Marginalen Bank",
              date: (Date.today + 15.days).to_datetime,
              content: "\n<p><strong>THIS IS A SCHEDULED EVENT Nov <var data-var='date'>8</var>, <var data-var='time'>21:00</var> UTC &nbsp;-&nbsp; Nov <var data-var='date'>9</var>, <var data-var='time'>05:00</var> UTC</strong></p><p><small>Oct <var data-var='date'> 8</var>, <var data-var='time'>07:41</var> UTC</small><br><strong>Scheduled</strong> - Swish - Marginalen Bank has an upcoming scheduled maintenance.<br /><br />Note: Stripe retrieves payment method planned maintenance information from payment method partners and the information may not be complete, accurate, or up to date.</p>      ",
              site_name: "Stripe",
              icon: "calendar_star"
            },
            {
              title: "United States SMS Carrier Partner Maintenance - T-Mobile",
              date: (Date.today + 24.days).to_datetime,
              content: "\n<p><strong>THIS IS A SCHEDULED EVENT Nov <var data-var='date'>5</var>, <var data-var='time'>16:00</var> - <var data-var='time'>20:00</var> PST</strong></p><p><small>Sep <var data-var='date'>23</var>, <var data-var='time'>17:27</var> PDT</small><br><strong>Scheduled</strong> - Our SMS carrier partner in the United States is conducting a planned maintenance from 05 November 2024 at 16:00 PST until 05 November 2024 at 20:00 PST. During the maintenance window, there could be intermittent delays delivering SMS to and from T-Mobile Network in the United States handsets.</p>      ",
              site_name: "Twilio",
              icon: "calendar_star"
            }
          ]
      },
      instance_name: 'Dependencies'
    },
    tempest_weather_station: {
      instance_name: 'Back Yard',
      temperature: 59,
      forecast: {
        right_now: { feels_like: 59, humidity: 28, icon: "clear-day", temperature: 59, sunrise: '07:01', sunset: '21:04', wind: { direction_cardinal: 'NE', gust: 3.2, units: 'mph' } },
        today: { icon: "clear-day", mintemp: 25, maxtemp: 61, day_override: 'Feb 19', conditions: "Clear", uv_index: 7, precip: { icon: 'chance-rain', probability: 0, amount: 0, units: 'in' } },
        tomorrow: { icon: "possibly-rainy-day", mintemp: 28, day_override: 'Feb 20', maxtemp: 59, conditions: "Rain Likely", uv_index: 0, precip: { icon: 'rainy', probability: 70 } },
      },
      weather_image: "#{Rails.application.credentials.base_url}/images/plugins/weather/wi-day-sunny.svg",
      today_weather_image: "https://tempestwx.com/images/Updated/possibly-thunderstorm-day.svg",
      tomorrow_weather_image: "https://tempestwx.com/images/Updated/partly-cloudy-day.svg",
      conditions: "Clear",
      humidity: 28,
      feels_like: 59
    },
    eight_sleep: {
      instance_name: 'Master Bedroom',
      metrics: {
        "name" => "week",
        "date" => "2025-01-24",
        "avg" =>
          [{ "name" => "sfs", "value" => "#{rand(38..92.5)}" },
           { "name" => "sqs", "value" => "80.5" },
           { "name" => "srs", "value" => "76.33" },
           { "name" => "sleep", "value" => "24885" },
           { "name" => "light", "value" => "11905" },
           { "name" => "rem", "value" => "7555" },
           { "name" => "rem_percent", "value" => "30.34" },
           { "name" => "deep", "value" => "5425" },
           { "name" => "deep_percent", "value" => "23.57" },
           { "name" => "hr", "value" => "48.5" },
           { "name" => "hrv", "value" => "54.75" },
           { "name" => "br", "value" => "15.43" },
           { "name" => "bedtime", "value" => "01:32:38" },
           { "name" => "wakeup", "value" => "08:50:28" },
           { "name" => "ttfa", "value" => "1160" },
           { "name" => "ttgu", "value" => "330" },
           { "name" => "snore", "value" => "140" },
           { "name" => "snore_heavy", "value" => "0" },
           { "name" => "snore_percent", "value" => "0.54" },
           { "name" => "snore_heavy_percent", "value" => "0" }],
        "stdDev" =>
          [{ "name" => "sfs", "value" => "12.97" },
           { "name" => "sqs", "value" => "16.62" },
           { "name" => "srs", "value" => "10.86" },
           { "name" => "sleep", "value" => "6730.74" },
           { "name" => "light", "value" => "5129.81" },
           { "name" => "rem", "value" => "2352.62" },
           { "name" => "rem_percent", "value" => "4.14" },
           { "name" => "deep", "value" => "1086.29" },
           { "name" => "deep_percent", "value" => "7.76" },
           { "name" => "hr", "value" => "1.38" },
           { "name" => "hrv", "value" => "4.03" },
           { "name" => "br", "value" => "0.46" },
           { "name" => "ttfa", "value" => "719.72" },
           { "name" => "ttgu", "value" => "579.14" },
           { "name" => "snore", "value" => "107.7" },
           { "name" => "snore_heavy", "value" => "0" },
           { "name" => "snore_percent", "value" => "0.39" },
           { "name" => "snore_heavy_percent", "value" => "0" }],
        "samples" =>
          [{ "date" => "2025-01-18",
             "avg" =>
               [{ "name" => "sfs", "value" => "92" },
                { "name" => "sqs", "value" => "100" },
                { "name" => "srs", "value" => "68" },
                { "name" => "sleep", "value" => "38040" },
                { "name" => "light", "value" => "20880" },
                { "name" => "rem", "value" => "12120" },
                { "name" => "rem_percent", "value" => "31.86" },
                { "name" => "deep", "value" => "5040" },
                { "name" => "deep_percent", "value" => "13.25" },
                { "name" => "hr", "value" => "51" },
                { "name" => "hrv", "value" => "49.4" },
                { "name" => "br", "value" => "16.3" },
                { "name" => "bedtime", "value" => "00:39:00" },
                { "name" => "wakeup", "value" => "11:51:00" },
                { "name" => "ttfa", "value" => "1170" },
                { "name" => "ttgu", "value" => "60" },
                { "name" => "snore", "value" => "180" },
                { "name" => "snore_heavy", "value" => "0" },
                { "name" => "snore_percent", "value" => "0.47" },
                { "name" => "snore_heavy_percent", "value" => "0" }] },
           { "date" => "2025-01-19",
             "avg" =>
               [{ "name" => "sfs", "value" => "95" },
                { "name" => "sqs", "value" => "95" },
                { "name" => "srs", "value" => "96" },
                { "name" => "sleep", "value" => "27780" },
                { "name" => "light", "value" => "15930" },
                { "name" => "rem", "value" => "8310" },
                { "name" => "rem_percent", "value" => "29.91" },
                { "name" => "deep", "value" => "3540" },
                { "name" => "deep_percent", "value" => "12.74" },
                { "name" => "hr", "value" => "48" },
                { "name" => "hrv", "value" => "51.4" },
                { "name" => "br", "value" => "15.8" },
                { "name" => "bedtime", "value" => "00:41:00" },
                { "name" => "wakeup", "value" => "08:55:00" },
                { "name" => "ttfa", "value" => "1740" },
                { "name" => "ttgu", "value" => "180" },
                { "name" => "snore", "value" => "300" },
                { "name" => "snore_heavy", "value" => "0" },
                { "name" => "snore_percent", "value" => "1.08" },
                { "name" => "snore_heavy_percent", "value" => "0" }] },
           { "date" => "2025-01-20",
             "avg" =>
               [{ "name" => "sfs", "value" => "81" },
                { "name" => "sqs", "value" => "86" },
                { "name" => "srs", "value" => "67" },
                { "name" => "sleep", "value" => "23220" },
                { "name" => "light", "value" => "12060" },
                { "name" => "rem", "value" => "5430" },
                { "name" => "rem_percent", "value" => "23.39" },
                { "name" => "deep", "value" => "5730" },
                { "name" => "deep_percent", "value" => "24.68" },
                { "name" => "hr", "value" => "47" },
                { "name" => "hrv", "value" => "61.8" },
                { "name" => "br", "value" => "15.2" },
                { "name" => "bedtime", "value" => "00:50:00" },
                { "name" => "wakeup", "value" => "08:07:00" },
                { "name" => "ttfa", "value" => "2430" },
                { "name" => "ttgu", "value" => "60" },
                { "name" => "snore", "value" => "240" },
                { "name" => "snore_heavy", "value" => "0" },
                { "name" => "snore_percent", "value" => "1.03" },
                { "name" => "snore_heavy_percent", "value" => "0" }] },
           { "date" => "2025-01-21",
             "avg" =>
               [{ "name" => "sfs", "value" => "56" },
                { "name" => "sqs", "value" => "49" },
                { "name" => "srs", "value" => "75" },
                { "name" => "sleep", "value" => "16590" },
                { "name" => "light", "value" => "6360" },
                { "name" => "rem", "value" => "5220" },
                { "name" => "rem_percent", "value" => "31.46" },
                { "name" => "deep", "value" => "5010" },
                { "name" => "deep_percent", "value" => "30.2" },
                { "name" => "hr", "value" => "49" },
                { "name" => "hrv", "value" => "57" },
                { "name" => "br", "value" => "15.1" },
                { "name" => "bedtime", "value" => "03:27:00" },
                { "name" => "wakeup", "value" => "08:13:30" },
                { "name" => "ttfa", "value" => "600" },
                { "name" => "ttgu", "value" => "30" },
                { "name" => "snore", "value" => "60" },
                { "name" => "snore_heavy", "value" => "0" },
                { "name" => "snore_percent", "value" => "0.36" },
                { "name" => "snore_heavy_percent", "value" => "0" }] },
           { "date" => "2025-01-22",
             "avg" =>
               [{ "name" => "sfs", "value" => "81" },
                { "name" => "sqs", "value" => "79" },
                { "name" => "srs", "value" => "85" },
                { "name" => "sleep", "value" => "22200" },
                { "name" => "light", "value" => "8940" },
                { "name" => "rem", "value" => "6270" },
                { "name" => "rem_percent", "value" => "28.24" },
                { "name" => "deep", "value" => "6990" },
                { "name" => "deep_percent", "value" => "31.49" },
                { "name" => "hr", "value" => "47" },
                { "name" => "hrv", "value" => "55.6" },
                { "name" => "br", "value" => "15" },
                { "name" => "bedtime", "value" => "00:47:00" },
                { "name" => "wakeup", "value" => "07:06:30" },
                { "name" => "ttfa", "value" => "510" },
                { "name" => "ttgu", "value" => "30" },
                { "name" => "snore", "value" => "0" },
                { "name" => "snore_heavy", "value" => "0" },
                { "name" => "snore_percent", "value" => "0" },
                { "name" => "snore_heavy_percent", "value" => "0" }] },
           { "date" => "2025-01-23",
             "avg" =>
               [{ "name" => "sfs", "value" => "72" },
                { "name" => "sqs", "value" => "74" },
                { "name" => "srs", "value" => "67" },
                { "name" => "sleep", "value" => "21480" },
                { "name" => "light", "value" => "7260" },
                { "name" => "rem", "value" => "7980" },
                { "name" => "rem_percent", "value" => "37.15" },
                { "name" => "deep", "value" => "6240" },
                { "name" => "deep_percent", "value" => "29.05" },
                { "name" => "hr", "value" => "49" },
                { "name" => "hrv", "value" => "53.3" },
                { "name" => "br", "value" => "15.2" },
                { "name" => "bedtime", "value" => "02:57:00" },
                { "name" => "wakeup", "value" => "09:04:00" },
                { "name" => "ttfa", "value" => "510" },
                { "name" => "ttgu", "value" => "1620" },
                { "name" => "snore", "value" => "60" },
                { "name" => "snore_heavy", "value" => "0" },
                { "name" => "snore_percent", "value" => "0.28" },
                { "name" => "snore_heavy_percent", "value" => "0" }] }] }
    },
    lunar_calendar: {
      current_phase: { name: "Full Moon" }, # Jan 13 is Full Moon
      next_phase: {
        name: "Waning Gibbous",
        date: "January 15" # Next phase after Full Moon
      },
      illumination: 99.8, # Nearly full illumination for Full Moon
      age: 15.0, # Age for Full Moon (falls within 13.8..16.2 range)
      next_full_moon: "February 11",
      next_new_moon: "January 28",
      phase_sequence: [
        { name: "Waxing Crescent", icon: "wi-moon-alt-waxing-crescent-3", current: false, date: "Jan 7", date_full: Date.parse('Jan 7') },
        { name: "First Quarter", icon: "wi-moon-alt-first-quarter", current: false, date: "Jan 10", date_full: Date.parse('Jan 10') },
        { name: "Waxing Gibbous", icon: "wi-moon-alt-waxing-gibbous-3", current: false, date: "Jan 12", date_full: Date.parse('Jan 12') },
        { name: "Full Moon", icon: "wi-moon-alt-full", current: true, date: "Jan 13", date_full: Date.parse('Jan 13') },
        { name: "Waning Gibbous", icon: "wi-moon-alt-waning-gibbous-3", current: false, date: "Jan 15", date_full: Date.parse('Jan 15') },
        { name: "Third Quarter", icon: "wi-moon-alt-third-quarter", current: false, date: "Jan 20", date_full: Date.parse('Jan 20') },
        { name: "Waning Crescent", icon: "wi-moon-alt-waning-crescent-3", current: false, date: "Jan 24", date_full: Date.parse('Jan 24') }
      ],
      instance_name: "Moon Phases"
    },
    todoist: {
      tasks: [
        { content: "Download Todoist on", due: "10 Feb" },
        { content: "Download Todoist on", due: "10 Feb" },
        { content: "Download Todoist on", due: "10 Feb" },
        { content: "Download Todoist on", due: "10 Feb" },
        { content: "Download Todoist on", due: "10 Feb" },
        { content: "Download Todoist on", due: "10 Feb" },
        { content: "Download Todoist on", due: "10 Feb" },
        { content: "Download Todoist on", due: "10 Feb" },
        { content: "Download Todoist on", due: "10 Feb" },
        { content: "Download Todoist on Download Todoist onDownload Todoist onDownload Todoist on", due: "10 Feb" },
        { content: "Take the [productivity method quiz](https://todoist.com/productivity-methods?utm_source=todoist&utm_medium=in_app&utm_campaign=onboarding_project&utm_content=inbox)", due: "11 Feb" },
        { content: "Take the [productivity method quiz](https://todoist.com/productivity-methods?utm_source=todoist&utm_medium=in_app&utm_campaign=onboarding_project&utm_content=inbox)", due: "11 Feb" },
        { content: "Take the [productivity method quiz](https://todoist.com/productivity-methods?utm_source=todoist&utm_medium=in_app&utm_campaign=onboarding_project&utm_content=inbox)", due: "11 Feb" },
        { content: "Browse the [Todoist Inspiration Hub](https://todoist.com/inspiration?utm_source=todoist&utm_medium=in_app&utm_campaign=onboarding_project&utm_content=inbox)", due: "12 Feb" },
        { content: "My Task", due: "10 Feb" },
        { content: "test", due: "12 Feb" }
      ],
      instance_name: "My task"
    },
    ticktick: {
      tasks: [
        { content: "Display error when polling", due: "10 Feb" },
        { content: "Native integration with HA", due: "12 Feb" },
        { content: "Native integration with Basecamp", due: "15 Feb" },
        { content: "Native integration with Ticktick", due: "11 Feb" },
        { content: "Native integration with Todoist", due: "2 Feb" },
        { content: "Increase server throughput", due: "10 Feb" },
        { content: "Improve DevX (developer experience)", due: "26 Feb" },
      ],
      instance_name: "My task"
    },
    google_tasks: {
      tasks: [
        { content: "Native integration with HA", due: Date.yesterday.strftime('%Y-%m-%d') },
        { content: "Increase server throughput", due: (Date.today + 2.days).strftime('%Y-%m-%d') },
        { content: "Gold/Metal Tracker Plugin", due: (Date.today + 3.days).strftime('%Y-%m-%d') },
        { content: "Improve DevX (developer experience)", due: (Date.today + 5.days).strftime('%Y-%m-%d') },
        { content: "Native Integration with Basecamp" },
        { content: "Native Integration with Basecamp" },
        { content: "Reduce S3 bill" },
      ],
      instance_name: "My Tasks"
    },
    basecamp: {
      tasks: [
        { content: "Native integration with HA", due: Date.yesterday.strftime('%Y-%m-%d') },
        { content: "Increase server throughput", due: (Date.today + 2.days).strftime('%Y-%m-%d') },
        { content: "Gold/Metal Tracker Plugin", due: (Date.today + 3.days).strftime('%Y-%m-%d') },
        { content: "Improve DevX (developer experience)", due: (Date.today + 5.days).strftime('%Y-%m-%d') },
        { content: "Native Integration with Basecamp" },
        { content: "Reduce S3 bill" },
      ],
      instance_name: "My Tasks"
    },
    parcel: {
      deliveries: [
        {
          title: "TRMNL",
          status_key: "out_for_delivery",
          latest: "Out for Delivery, Expected Delivery by 9:00pm",
          delivery_by: "Friday",
          days: 0,
        },
        {
          title: "New socks",
          status_key: "in_transit",
          latest: "Arrived at USPS Regional Facility",
          delivery_by: "Monday",
          days: 3,
        },
      ],
      error: nil,
      instance_name: "My packages",
      filter_mode: 'Active',
      style: 'detailed'
    },
   changelog: {
     instance_name: 'TRMNL Changelog',
     items: [
     {date: "May 27",
      title: "Hide the title bar",
      body:
       "Visit your account to enable."},
     {date: "May 27",
      title: "ChatGPT improvement",
      body: "Select the model that's right for you."},
     {date: "May 25",
      title: "New Recipe: Healthchecks.io",
      body: "Created by community member Riccardo. <a href='' class='underline'>Enable</a> and aim for 7 9's."},
     {date: "May 22",
      title: "Introducing Reusable Markup",
      body: "Define paritals, common styelsheets, JS and more for faster plugin development."},
    ]
   }
  }.freeze
  # rubocop:enable all
end
# rubocop:enable Metrics/ModuleLength
