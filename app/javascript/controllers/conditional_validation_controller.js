import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["conditionalField", "fieldContainer"]
  static values = {
    errorClass: { type: String, default: "border-red-500" },
    hiddenClass: { type: String, default: "hidden" },
    containerClass: { type: String, default: ".p-6" }
  }
  static classes = ["error", "hidden"]

  connect() {
    console.log("Connecting conditional validation controller...")
    this.setupEventListeners()

    // Initialize immediately if visible, or wait for visibility
    if (this.isVisible()) {
      this.initializeFields()
    } else {
      this.observeVisibility()
    }
  }

  isVisible() {
    return this.element.offsetParent !== null
  }

  observeVisibility() {
    // Use MutationObserver to watch for visibility changes
    const observer = new MutationObserver(() => {
      if (this.isVisible()) {
        console.log("Element became visible, initializing fields...")
        this.initializeFields()
        observer.disconnect()
      }
    })

    // Watch for class changes on parent elements (like accordion opening)
    let element = this.element
    while (element) {
      observer.observe(element, {
        attributes: true,
        attributeFilter: ["class", "style"],
        subtree: false
      })
      element = element.parentElement
    }
  }

  setupEventListeners() {
    this.controllingFields.forEach((field) => {
      field.addEventListener("change", this.handleFieldChange.bind(this))
    })
  }

  handleFieldChange() {
    this.validateAllFields()
  }

  initializeFields() {
    console.log("Initializing conditional validation fields...")
    this.clearAllValidationStates()
    this.applyAllValidationRules()
  }

  validateAllFields() {
    this.resetAllFields()
    this.applyAllValidationRules()
  }

  resetAllFields() {
    this.showAllContainers()
    this.clearAllValidationStates()
  }

  showAllContainers() {
    this.fieldContainerTargets.forEach((container) => {
      container.classList.remove(this.hiddenClass)
    })
  }

  clearAllValidationStates() {
    this.conditionalFieldTargets.forEach((field) => {
      field.classList.remove(this.errorClassValue)
      field.required = false
    })
  }

  applyAllValidationRules() {
    console.log("Applying validation rules...")
    console.log("Rules elements found:", this.rulesElements.length)
    this.rulesElements.forEach((element) => {
      const rules = this.parseRules(element)
      console.log("Parsed rules:", rules)
      if (rules) {
        this.applyValidationRules(rules)
      }
    })
  }

  applyValidationRules(rules) {
    console.log("Applying rules for field:", rules.field)
    const controllingField = this.findControllingField(rules.field)
    console.log("Controlling field found:", controllingField)
    if (!controllingField) {
      console.log("No controlling field found for:", rules.field)
      return
    }

    console.log("Controlling field value:", controllingField.value)
    const condition = this.findMatchingCondition(rules, controllingField.value)
    console.log("Matching condition:", condition)
    if (!condition) {
      console.log("No matching condition for value:", controllingField.value)
      return
    }

    this.handleRequiredFields(condition.required || [])
    this.handleHiddenFields(condition.hidden || [])
  }

  handleRequiredFields(requiredFields) {
    requiredFields.forEach((fieldName) => {
      const targetField = this.findTargetField(fieldName)
      if (targetField) {
        targetField.required = true
        if (this.isFieldEmpty(targetField)) {
          targetField.classList.add(this.errorClassValue)
        }
      }
    })
  }

  handleHiddenFields(hiddenFields) {
    console.log("Handling hidden fields:", hiddenFields)
    hiddenFields.forEach((fieldName) => {
      console.log("Looking for target field:", fieldName)
      const targetField = this.findTargetField(fieldName)
      console.log("Target field found:", targetField)
      if (targetField) {
        const container = this.findFieldContainer(targetField)
        console.log("Container found:", container)
        if (container) {
          console.log("Hiding container for field:", fieldName)
          container.classList.add(this.hiddenClass)
          targetField.required = false
          targetField.classList.remove(this.errorClassValue)
        }
      }
    })
  }

  // Helper methods
  get controllingFields() {
    const fields = new Set()
    this.rulesElements.forEach((element) => {
      const rules = this.parseRules(element)
      if (rules) {
        const field = this.findControllingField(rules.field)
        if (field) fields.add(field)
      }
    })
    return Array.from(fields)
  }

  get rulesElements() {
    return this.element.querySelectorAll("[data-conditional-validation-rules]")
  }

  parseRules(element) {
    try {
      return JSON.parse(element.dataset.conditionalValidationRules)
    } catch (e) {
      console.warn("Invalid conditional validation rules:", e)
      return null
    }
  }

  findControllingField(fieldName) {
    const selector = `[name*="${fieldName}"]`
    console.log("Looking for controlling field with selector:", selector)
    const field = this.element.querySelector(selector)
    console.log("Found controlling field:", field)
    return field
  }

  findTargetField(fieldName) {
    const selector = `[name*="[${fieldName}]"]`
    console.log("Looking for target field with selector:", selector)
    const field = this.element.querySelector(selector)
    console.log("Found target field:", field)
    return field
  }

  findMatchingCondition(rules, value) {
    return rules.conditions.find((condition) => condition.when === value)
  }

  findFieldContainer(field) {
    return this.fieldContainerTargets.find((container) => container.contains(field))
  }

  isFieldEmpty(field) {
    return !field.value.trim()
  }
}
