import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["conditionalField", "fieldContainer"]
  static values = {
    errorClass: { type: String, default: "border-red-500" },
    hiddenClass: { type: String, default: "hidden" },
    containerClass: { type: String, default: ".p-6" }
  }
  static classes = ["error", "hidden"]

  connect() {
    this.setupEventListeners()
    this.initializeFields()
  }

  setupEventListeners() {
    this.controllingFields.forEach((field) => {
      field.addEventListener("change", this.handleFieldChange.bind(this))
    })
  }

  handleFieldChange(event) {
    this.validateAllFields()
  }

  validateAllFields() {
    this.resetAllFields()
    this.applyAllValidationRules()
  }

  resetAllFields() {
    this.showAllContainers()
    this.clearAllValidationStates()
  }

  showAllContainers() {
    this.fieldContainerTargets.forEach((container) => {
      container.classList.remove(this.hiddenClass)
    })
  }

  clearAllValidationStates() {
    this.conditionalFieldTargets.forEach((field) => {
      field.classList.remove(this.errorClassValue)
      field.required = false
    })
  }

  applyAllValidationRules() {
    this.rulesElements.forEach((element) => {
      const rules = this.parseRules(element)
      if (rules) {
        this.applyValidationRules(rules)
      }
    })
  }

  applyValidationRules(rules) {
    const controllingField = this.findControllingField(rules.field)
    if (!controllingField) return

    const condition = this.findMatchingCondition(rules, controllingField.value)
    if (!condition) return

    this.handleRequiredFields(condition.required || [])
    this.handleHiddenFields(condition.hidden || [])
  }

  handleRequiredFields(requiredFields) {
    requiredFields.forEach((fieldName) => {
      const targetField = this.findTargetField(fieldName)
      if (targetField) {
        targetField.required = true
        if (this.isFieldEmpty(targetField)) {
          targetField.classList.add(this.errorClassValue)
        }
      }
    })
  }

  handleHiddenFields(hiddenFields) {
    hiddenFields.forEach((fieldName) => {
      const targetField = this.findTargetField(fieldName)
      if (targetField) {
        const container = this.findFieldContainer(targetField)
        if (container) {
          container.classList.add(this.hiddenClass)
          targetField.required = false
          targetField.classList.remove(this.errorClassValue)
        }
      }
    })
  }

  // Helper methods
  get controllingFields() {
    const fields = new Set()
    this.rulesElements.forEach((element) => {
      const rules = this.parseRules(element)
      if (rules) {
        const field = this.findControllingField(rules.field)
        if (field) fields.add(field)
      }
    })
    return Array.from(fields)
  }

  get rulesElements() {
    return this.element.querySelectorAll("[data-conditional-validation-rules]")
  }

  parseRules(element) {
    try {
      return JSON.parse(element.dataset.conditionalValidationRules)
    } catch (e) {
      console.warn("Invalid conditional validation rules:", e)
      return null
    }
  }

  findControllingField(fieldName) {
    return this.element.querySelector(`[name*="${fieldName}"]`)
  }

  findTargetField(fieldName) {
    return this.element.querySelector(`[name*="[${fieldName}]"]`)
  }

  findMatchingCondition(rules, value) {
    return rules.conditions.find((condition) => condition.when === value)
  }

  findFieldContainer(field) {
    return this.fieldContainerTargets.find((container) => container.contains(field))
  }

  isFieldEmpty(field) {
    return !field.value.trim()
  }
}
